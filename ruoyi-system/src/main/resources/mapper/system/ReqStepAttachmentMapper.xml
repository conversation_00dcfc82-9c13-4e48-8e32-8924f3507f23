<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ReqStepAttachmentMapper">
    
    <resultMap type="ReqStepAttachment" id="ReqStepAttachmentResult">
        <result property="attachmentId"    column="attachment_id"    />
        <result property="stepId"    column="step_id"    />
        <result property="attachmentName"    column="attachment_name"    />
        <result property="attachmentPath"    column="attachment_path"    />
        <result property="attachmentUrl"    column="attachment_url"    />
        <result property="attachmentSize"    column="attachment_size"    />
        <result property="attachmentType"    column="attachment_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectReqStepAttachmentVo">
        select attachment_id, step_id, attachment_name, attachment_path, attachment_url, attachment_size, attachment_type, del_flag, create_by, create_time, update_by, update_time, remark from req_step_attachment
    </sql>

    <select id="selectReqStepAttachmentList" parameterType="ReqStepAttachment" resultMap="ReqStepAttachmentResult">
        <include refid="selectReqStepAttachmentVo"/>
        <where>  
            <if test="stepId != null "> and step_id = #{stepId}</if>
            <if test="attachmentName != null  and attachmentName != ''"> and attachment_name like concat('%', #{attachmentName}, '%')</if>
            <if test="attachmentPath != null  and attachmentPath != ''"> and attachment_path = #{attachmentPath}</if>
            <if test="attachmentType != null  and attachmentType != ''"> and attachment_type = #{attachmentType}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectReqStepAttachmentByStepId" parameterType="Long" resultMap="ReqStepAttachmentResult">
        <include refid="selectReqStepAttachmentVo"/>
        where step_id = #{stepId} and del_flag = '0'
    </select>
    
    <select id="selectReqStepAttachmentByAttachmentId" parameterType="Long" resultMap="ReqStepAttachmentResult">
        <include refid="selectReqStepAttachmentVo"/>
        where attachment_id = #{attachmentId} and del_flag = '0'
    </select>
        
    <insert id="insertReqStepAttachment" parameterType="ReqStepAttachment" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into req_step_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stepId != null">step_id,</if>
            <if test="attachmentName != null">attachment_name,</if>
            <if test="attachmentPath != null">attachment_path,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="attachmentSize != null">attachment_size,</if>
            <if test="attachmentType != null">attachment_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stepId != null">#{stepId},</if>
            <if test="attachmentName != null">#{attachmentName},</if>
            <if test="attachmentPath != null">#{attachmentPath},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="attachmentSize != null">#{attachmentSize},</if>
            <if test="attachmentType != null">#{attachmentType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateReqStepAttachment" parameterType="ReqStepAttachment">
        update req_step_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="stepId != null">step_id = #{stepId},</if>
            <if test="attachmentName != null">attachment_name = #{attachmentName},</if>
            <if test="attachmentPath != null">attachment_path = #{attachmentPath},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
            <if test="attachmentSize != null">attachment_size = #{attachmentSize},</if>
            <if test="attachmentType != null">attachment_type = #{attachmentType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where attachment_id = #{attachmentId}
    </update>

    <delete id="deleteReqStepAttachmentByAttachmentId" parameterType="Long">
        update req_step_attachment set del_flag = '2' where attachment_id = #{attachmentId}
    </delete>

    <delete id="deleteReqStepAttachmentByAttachmentIds" parameterType="String">
        update req_step_attachment set del_flag = '2' where attachment_id in 
        <foreach item="attachmentId" collection="array" open="(" separator="," close=")">
            #{attachmentId}
        </foreach>
    </delete>
    
    <delete id="deleteReqStepAttachmentByStepId" parameterType="Long">
        update req_step_attachment set del_flag = '2' where step_id = #{stepId}
    </delete>
</mapper> 