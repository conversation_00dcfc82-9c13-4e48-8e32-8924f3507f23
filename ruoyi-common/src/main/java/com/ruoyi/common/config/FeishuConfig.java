package com.ruoyi.common.config;

import com.lark.oapi.Client;
import com.lark.oapi.core.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 飞书SDK配置类
 * 基于官方larksuite/oapi-sdk-java 2.4.20版本
 * 提供飞书应用凭证管理和客户端初始化
 * 
 * <AUTHOR>
 */
@Configuration
public class FeishuConfig {
    
    private static final Logger log = LoggerFactory.getLogger(FeishuConfig.class);

    /**
     * 飞书应用配置属性
     */
    @Component
    @ConfigurationProperties(prefix = "feishu")
    public static class FeishuProperties {
        /**
         * 飞书应用ID
         */
        private String appId = "cli_a8f89e71adfc100e";

        /**
         * 飞书应用密钥
         */
        private String appSecret = "RCVwevPg6Dbx4p9NxstCTfMT44bCTP5G";
        
        /**
         * 加密密钥 (Encrypt Key)
         */
        private String encryptKey;
        
        /**
         * 验证令牌 (Verification Token)
         */
        private String verificationToken;
        
        /**
         * 是否开启调试模式
         */
        private boolean debug = false;
        
        /**
         * 请求超时时间 (毫秒)
         */
        private long requestTimeout = 30000;

        // Getters and Setters
        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppSecret() {
            return appSecret;
        }

        public void setAppSecret(String appSecret) {
            this.appSecret = appSecret;
        }

        public String getEncryptKey() {
            return encryptKey;
        }

        public void setEncryptKey(String encryptKey) {
            this.encryptKey = encryptKey;
        }

        public String getVerificationToken() {
            return verificationToken;
        }

        public void setVerificationToken(String verificationToken) {
            this.verificationToken = verificationToken;
        }

        public boolean isDebug() {
            return debug;
        }

        public void setDebug(boolean debug) {
            this.debug = debug;
        }

        public long getRequestTimeout() {
            return requestTimeout;
        }

        public void setRequestTimeout(long requestTimeout) {
            this.requestTimeout = requestTimeout;
        }
    }

    /**
     * 创建飞书客户端Bean
     * 基于官方SDK的Client.newBuilder()方式构建
     * 
     * @param properties 飞书配置属性
     * @return 飞书客户端实例
     */
    @Bean
    public Client feishuClient(FeishuProperties properties) {
        try {
            log.info("正在初始化飞书SDK客户端...");
            
            // 验证必要的配置参数
            if (properties.getAppId() == null || properties.getAppId().isEmpty()) {
                log.warn("飞书应用ID未配置，将使用空配置创建客户端");
                return null;
            }
            
            if (properties.getAppSecret() == null || properties.getAppSecret().isEmpty()) {
                log.warn("飞书应用密钥未配置，将使用空配置创建客户端");
                return null;
            }

            // 基于官方SDK创建Client
            Client client = Client.newBuilder(properties.getAppId(), properties.getAppSecret())
                    .logReqAtDebug(properties.isDebug()) // 设置调试日志
                    .build();

            log.info("飞书SDK客户端初始化成功 - AppId: {}", properties.getAppId());
            return client;
            
        } catch (Exception e) {
            log.error("飞书SDK客户端初始化失败", e);
            return null;
        }
    }

    /**
     * 创建飞书配置Bean
     * 提供原始配置信息访问
     * 
     * @param properties 飞书配置属性
     * @return 飞书配置实例
     */
    @Bean
    public Config feishuSdkConfig(FeishuProperties properties) {
        try {
            Config config = new Config();
            // 可以在这里设置更多配置选项
            return config;
        } catch (Exception e) {
            log.error("飞书配置创建失败", e);
            return null;
        }
    }
} 