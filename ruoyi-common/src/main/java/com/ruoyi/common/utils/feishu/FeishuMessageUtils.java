package com.ruoyi.common.utils.feishu;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

/**
 * 飞书消息发送工具类
 * 
 * <AUTHOR>
 */
public class FeishuMessageUtils {
    private static final Logger log = LoggerFactory.getLogger(FeishuMessageUtils.class);

    /**
     * 飞书群聊机器人Webhook URL前缀
     */
    private static final String FEISHU_WEBHOOK_URL_PREFIX = "https://open.feishu.cn/open-apis/bot/v2/hook/";
    
    /**
     * 发送文本消息到飞书群聊
     *
     * @param webhookKey 飞书群聊机器人的webhook key
     * @param content 消息内容
     * @return 发送结果，true成功，false失败
     */
    public static boolean sendTextMessage(String webhookKey, String content) {
        if (StringUtils.isEmpty(webhookKey) || StringUtils.isEmpty(content)) {
            log.error("发送飞书消息失败: webhookKey或消息内容为空");
            return false;
        }
        
        // 构建请求URL
        String webhookUrl = FEISHU_WEBHOOK_URL_PREFIX + webhookKey;
        
        // 构建消息内容
        JSONObject message = new JSONObject();
        message.put("msg_type", "text");
        
        JSONObject text = new JSONObject();
        text.put("content", content);
        message.put("content", text);
        
        String param = JSON.toJSONString(message);
        try {
            // 发送POST请求
            String result = HttpUtils.sendPost(webhookUrl, param, MediaType.APPLICATION_JSON_VALUE);
            JSONObject resultJson = JSON.parseObject(result);
            
            // 判断是否发送成功
            if (resultJson != null && resultJson.getIntValue("code") == 0) {
                log.info("飞书消息发送成功: {}", content);
                return true;
            } else {
                log.error("飞书消息发送失败: {}, 错误信息: {}", content, result);
                return false;
            }
        } catch (Exception e) {
            log.error("飞书消息发送异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送富文本消息到飞书群聊
     *
     * @param webhookKey 飞书群聊机器人的webhook key
     * @param title 富文本标题
     * @param content 富文本内容
     * @return 发送结果，true成功，false失败
     */
    public static boolean sendRichTextMessage(String webhookKey, String title, String content) {
        if (StringUtils.isEmpty(webhookKey) || StringUtils.isEmpty(content)) {
            log.error("发送飞书富文本消息失败: webhookKey或消息内容为空");
            return false;
        }
        
        // 构建请求URL
        String webhookUrl = FEISHU_WEBHOOK_URL_PREFIX + webhookKey;
        
        // 构建markdown消息
        JSONObject message = new JSONObject();
        message.put("msg_type", "interactive");
        
        // 构建卡片内容
        JSONObject card = new JSONObject();
        
        // 设置卡片标题
        JSONObject header = new JSONObject();
        header.put("title", new JSONObject().fluentPut("content", title).fluentPut("tag", "plain_text"));
        card.put("header", header);
        
        // 设置卡片内容
        JSONArray elements = new JSONArray();
        
        // 添加Markdown元素
        JSONObject markdownElement = new JSONObject();
        markdownElement.put("tag", "markdown");
        markdownElement.put("content", content);
        elements.add(markdownElement);
        
        card.put("elements", elements);
        message.put("card", card);
        
        String param = JSON.toJSONString(message);
        try {
            log.info("飞书消息参数: {}", param);
            // 发送POST请求
            String result = HttpUtils.sendPost(webhookUrl, param, MediaType.APPLICATION_JSON_VALUE);
            JSONObject resultJson = JSON.parseObject(result);
            
            // 判断是否发送成功
            if (resultJson != null && resultJson.getIntValue("code") == 0) {
                log.info("飞书富文本消息发送成功: {}", title);
                return true;
            } else {
                log.error("飞书富文本消息发送失败: {}, 错误信息: {}", title, result);
                return false;
            }
        } catch (Exception e) {
            log.error("飞书富文本消息发送异常: {}", e.getMessage());
            return false;
        }
    }
} 