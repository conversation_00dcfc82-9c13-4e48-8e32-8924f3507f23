<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.data.mapper.AccountTradeDetailMapper">
    
    <resultMap type="AccountTradeDetail" id="AccountTradeDetailResult">
        <result property="id"    column="id"    />
        <result property="tradeSerial"    column="trade_serial"    />
        <result property="payAccount"    column="pay_account"    />
        <result property="incomeType"    column="income_type"    />
        <result property="tradeType"    column="trade_type"    />
        <result property="tradeStatus"    column="trade_status"    />
        <result property="tradeAmount"    column="trade_amount"    />
        <result property="currency"    column="currency"    />
        <result property="tradeTime"    column="trade_time"    />
        <result property="availableAmount"    column="available_amount"    />
        <result property="frozenAmount"    column="frozen_amount"    />
        <result property="accountAmount"    column="account_amount"    />
        <result property="pendingAmount"    column="pending_amount"    />
        <result property="deposit"    column="deposit"    />
        <result property="orderCode"    column="order_code"    />
        <result property="subOrderCode"    column="sub_order_code"    />
        <result property="subOrderType"    column="sub_order_type"    />
        <result property="remark"    column="remark"    />
        <result property="orderNo"    column="order_no"    />
        <result property="shop"    column="shop"    />
        <result property="flowStatus"    column="flow_status"    />
        <result property="operationCreateTime"    column="operation_create_time"    />
        <result property="operationUpdateTime"    column="operation_update_time"    />
        <result property="executionLog"    column="execution_log"    />
        <result property="logCategories"    column="log_categories"    />
    </resultMap>

    <sql id="selectAccountTradeDetailVo">
        select id, trade_serial, pay_account, income_type, trade_type, trade_status, trade_amount, currency, trade_time, available_amount, frozen_amount, account_amount, pending_amount, deposit, order_code, sub_order_code, sub_order_type, remark, order_no, shop, flow_status, operation_create_time, operation_update_time, execution_log, log_categories from account_trade_detail
    </sql>

    <select id="selectAccountTradeDetailList" parameterType="AccountTradeDetail" resultMap="AccountTradeDetailResult">
        <include refid="selectAccountTradeDetailVo"/>
        <include refid="selectAccountTradeDetailWhere"/>
    </select>
    
    <select id="selectAccountTradeDetailById" parameterType="Long" resultMap="AccountTradeDetailResult">
        <include refid="selectAccountTradeDetailVo"/>
        where id = #{id}
    </select>

    <!-- 统计账户交易明细数量（优化版本） -->
    <select id="countAccountTradeDetail" parameterType="AccountTradeDetail" resultType="long">
        SELECT /*+ USE_INDEX(account_trade_detail, idx_trade_time_flow_status) */
        count(*) from account_trade_detail
        <include refid="selectAccountTradeDetailWhere"/>
    </select>

    <!-- 分页查询账户交易明细列表（用于流式导出全量数据） -->
    <select id="selectAccountTradeDetailListByPage" parameterType="AccountTradeDetail" resultMap="AccountTradeDetailResult">
        SELECT /*+ USE_INDEX(account_trade_detail, idx_trade_time_flow_status) */
        id, trade_serial, pay_account, income_type, trade_type, trade_status, 
        trade_amount, currency, trade_time, available_amount, frozen_amount, 
        account_amount, pending_amount, deposit, order_code, sub_order_code, 
        sub_order_type, remark, order_no, shop, flow_status, 
        operation_create_time, operation_update_time, execution_log, log_categories 
        from account_trade_detail
        <include refid="selectAccountTradeDetailWhere"/>
        order by id asc
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 抽取WHERE条件，用于list查询和count查询复用（添加索引友好的条件顺序） -->
    <sql id="selectAccountTradeDetailWhere">
        <where>  
            <!-- 优先使用索引字段进行过滤 -->
            <if test="params.beginTradeTime != null and params.beginTradeTime != '' and params.endTradeTime != null and params.endTradeTime != ''"> 
                and trade_time between #{params.beginTradeTime} and #{params.endTradeTime}
            </if>
            <if test="flowStatus != null "> and flow_status = #{flowStatus}</if>
            
            <!-- 其他条件 -->
            <if test="tradeSerial != null  and tradeSerial != ''"> and trade_serial = #{tradeSerial}</if>
            <if test="payAccount != null  and payAccount != ''"> and pay_account = #{payAccount}</if>
            <if test="incomeType != null  and incomeType != ''"> and income_type = #{incomeType}</if>
            <if test="tradeType != null  and tradeType != ''"> and trade_type = #{tradeType}</if>
            <if test="tradeStatus != null  and tradeStatus != ''"> and trade_status = #{tradeStatus}</if>
            <if test="tradeAmount != null "> and trade_amount = #{tradeAmount}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="availableAmount != null "> and available_amount = #{availableAmount}</if>
            <if test="frozenAmount != null "> and frozen_amount = #{frozenAmount}</if>
            <if test="accountAmount != null "> and account_amount = #{accountAmount}</if>
            <if test="pendingAmount != null "> and pending_amount = #{pendingAmount}</if>
            <if test="deposit != null "> and deposit = #{deposit}</if>
            <if test="orderCode != null  and orderCode != ''"> and order_code = #{orderCode}</if>
            <if test="subOrderCode != null  and subOrderCode != ''"> and sub_order_code = #{subOrderCode}</if>
            <if test="subOrderType != null  and subOrderType != ''"> and sub_order_type = #{subOrderType}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="shop != null  and shop != ''"> and shop = #{shop}</if>
            <if test="logCategories != null and logCategories != ''"> and log_categories LIKE CONCAT('%', #{logCategories}, '%')</if>
            <if test="executionLogKeyword != null and executionLogKeyword != ''"> and execution_log LIKE CONCAT('%', #{executionLogKeyword}, '%')</if>
        </where>
    </sql>

    <insert id="insertAccountTradeDetail" parameterType="AccountTradeDetail" useGeneratedKeys="true" keyProperty="id">
        insert into account_trade_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tradeSerial != null">trade_serial,</if>
            <if test="payAccount != null">pay_account,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="tradeType != null">trade_type,</if>
            <if test="tradeStatus != null">trade_status,</if>
            <if test="tradeAmount != null">trade_amount,</if>
            <if test="currency != null">currency,</if>
            <if test="tradeTime != null">trade_time,</if>
            <if test="availableAmount != null">available_amount,</if>
            <if test="frozenAmount != null">frozen_amount,</if>
            <if test="accountAmount != null">account_amount,</if>
            <if test="pendingAmount != null">pending_amount,</if>
            <if test="deposit != null">deposit,</if>
            <if test="orderCode != null">order_code,</if>
            <if test="subOrderCode != null">sub_order_code,</if>
            <if test="subOrderType != null">sub_order_type,</if>
            <if test="remark != null">remark,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="shop != null">shop,</if>
            <if test="flowStatus != null">flow_status,</if>
            <if test="operationCreateTime != null">operation_create_time,</if>
            <if test="operationUpdateTime != null">operation_update_time,</if>
            <if test="executionLog != null">execution_log,</if>
            <if test="logCategories != null">log_categories,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tradeSerial != null">#{tradeSerial},</if>
            <if test="payAccount != null">#{payAccount},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="tradeType != null">#{tradeType},</if>
            <if test="tradeStatus != null">#{tradeStatus},</if>
            <if test="tradeAmount != null">#{tradeAmount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="tradeTime != null">#{tradeTime},</if>
            <if test="availableAmount != null">#{availableAmount},</if>
            <if test="frozenAmount != null">#{frozenAmount},</if>
            <if test="accountAmount != null">#{accountAmount},</if>
            <if test="pendingAmount != null">#{pendingAmount},</if>
            <if test="deposit != null">#{deposit},</if>
            <if test="orderCode != null">#{orderCode},</if>
            <if test="subOrderCode != null">#{subOrderCode},</if>
            <if test="subOrderType != null">#{subOrderType},</if>
            <if test="remark != null">#{remark},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="shop != null">#{shop},</if>
            <if test="flowStatus != null">#{flowStatus},</if>
            <if test="operationCreateTime != null">#{operationCreateTime},</if>
            <if test="operationUpdateTime != null">#{operationUpdateTime},</if>
            <if test="executionLog != null">#{executionLog},</if>
            <if test="logCategories != null">#{logCategories},</if>
        </trim>
    </insert>

    <update id="updateAccountTradeDetail" parameterType="AccountTradeDetail">
        update account_trade_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="tradeSerial != null">trade_serial = #{tradeSerial},</if>
            <if test="payAccount != null">pay_account = #{payAccount},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="tradeType != null">trade_type = #{tradeType},</if>
            <if test="tradeStatus != null">trade_status = #{tradeStatus},</if>
            <if test="tradeAmount != null">trade_amount = #{tradeAmount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="tradeTime != null">trade_time = #{tradeTime},</if>
            <if test="availableAmount != null">available_amount = #{availableAmount},</if>
            <if test="frozenAmount != null">frozen_amount = #{frozenAmount},</if>
            <if test="accountAmount != null">account_amount = #{accountAmount},</if>
            <if test="pendingAmount != null">pending_amount = #{pendingAmount},</if>
            <if test="deposit != null">deposit = #{deposit},</if>
            <if test="orderCode != null">order_code = #{orderCode},</if>
            <if test="subOrderCode != null">sub_order_code = #{subOrderCode},</if>
            <if test="subOrderType != null">sub_order_type = #{subOrderType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="shop != null">shop = #{shop},</if>
            <if test="flowStatus != null">flow_status = #{flowStatus},</if>
            <if test="operationCreateTime != null">operation_create_time = #{operationCreateTime},</if>
            <if test="operationUpdateTime != null">operation_update_time = #{operationUpdateTime},</if>
            <if test="executionLog != null">execution_log = #{executionLog},</if>
            <if test="logCategories != null">log_categories = #{logCategories},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAccountTradeDetailById" parameterType="Long">
        delete from account_trade_detail where id = #{id}
    </delete>

    <delete id="deleteAccountTradeDetailByIds" parameterType="String">
        delete from account_trade_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据交易流水号查询账户交易明细 -->
    <select id="selectAccountTradeDetailByTradeSerial" parameterType="String" resultMap="AccountTradeDetailResult">
        <include refid="selectAccountTradeDetailVo"/>
        where trade_serial = #{tradeSerial}
    </select>

    <!-- 批量插入账户交易明细 -->
    <insert id="batchInsertAccountTradeDetail" parameterType="java.util.List">
        insert into account_trade_detail
        (trade_serial, pay_account, income_type, trade_type, trade_status, trade_amount, currency, 
         trade_time, available_amount, frozen_amount, account_amount, pending_amount, deposit, 
         order_code, sub_order_code, sub_order_type, remark, order_no, shop, flow_status,
         operation_create_time, operation_update_time, execution_log)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tradeSerial}, #{item.payAccount}, #{item.incomeType}, #{item.tradeType}, 
             #{item.tradeStatus}, #{item.tradeAmount}, #{item.currency}, #{item.tradeTime}, 
             #{item.availableAmount}, #{item.frozenAmount}, #{item.accountAmount}, #{item.pendingAmount}, 
             #{item.deposit}, #{item.orderCode}, #{item.subOrderCode}, #{item.subOrderType}, 
             #{item.remark}, #{item.orderNo}, #{item.shop}, #{item.flowStatus},
             COALESCE(#{item.operationCreateTime}, NOW()), NOW(), #{item.executionLog})
        </foreach>
    </insert>

    <!-- 批量更新账户交易明细 -->
    <update id="batchUpdateAccountTradeDetail" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update account_trade_detail
            <set>
                <if test="item.payAccount != null">pay_account = #{item.payAccount},</if>
                <if test="item.incomeType != null">income_type = #{item.incomeType},</if>
                <if test="item.tradeType != null">trade_type = #{item.tradeType},</if>
                <if test="item.tradeStatus != null">trade_status = #{item.tradeStatus},</if>
                <if test="item.tradeAmount != null">trade_amount = #{item.tradeAmount},</if>
                <if test="item.currency != null">currency = #{item.currency},</if>
                <if test="item.tradeTime != null">trade_time = #{item.tradeTime},</if>
                <if test="item.availableAmount != null">available_amount = #{item.availableAmount},</if>
                <if test="item.frozenAmount != null">frozen_amount = #{item.frozenAmount},</if>
                <if test="item.accountAmount != null">account_amount = #{item.accountAmount},</if>
                <if test="item.pendingAmount != null">pending_amount = #{item.pendingAmount},</if>
                <if test="item.deposit != null">deposit = #{item.deposit},</if>
                <if test="item.orderCode != null">order_code = #{item.orderCode},</if>
                <if test="item.subOrderCode != null">sub_order_code = #{item.subOrderCode},</if>
                <if test="item.subOrderType != null">sub_order_type = #{item.subOrderType},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.orderNo != null">order_no = #{item.orderNo},</if>
                <if test="item.shop != null">shop = #{item.shop},</if>
                <if test="item.flowStatus != null">flow_status = #{item.flowStatus},</if>
                operation_update_time = NOW(),
                <if test="item.executionLog != null">execution_log = #{item.executionLog},</if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <!-- 批量更新账户交易明细（优化版，使用CASE WHEN） -->
    <update id="batchUpdateAccountTradeDetailOptimized" parameterType="java.util.List">
        update account_trade_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="pay_account = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.payAccount != null">
                        when id = #{item.id} then #{item.payAccount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="income_type = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.incomeType != null">
                        when id = #{item.id} then #{item.incomeType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="trade_type = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.tradeType != null">
                        when id = #{item.id} then #{item.tradeType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="flow_status = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.flowStatus != null">
                        when id = #{item.id} then #{item.flowStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="shop = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.shop != null">
                        when id = #{item.id} then #{item.shop}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderNo != null">
                        when id = #{item.id} then #{item.orderNo}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.remark != null">
                        when id = #{item.id} then #{item.remark}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in 
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 根据交易流水号批量查询账户交易明细 -->
    <select id="selectAccountTradeDetailByTradeSerials" parameterType="java.util.List" resultMap="AccountTradeDetailResult">
        <include refid="selectAccountTradeDetailVo"/>
        where trade_serial in
        <foreach collection="list" item="tradeSerial" open="(" separator="," close=")">
            #{tradeSerial}
        </foreach>
    </select>

    <!-- 查询待处理的账户交易明细（flow_status为0、1或4） -->
    <select id="selectPendingAccountTradeDetails" resultMap="AccountTradeDetailResult">
        <include refid="selectAccountTradeDetailVo"/>
        where flow_status in (0, 1, 4)
        and trade_type is not null and trade_type != ''
        order by id asc
    </select>

    <!-- 根据交易单号查询有店铺信息的记录（用于店铺名推导） -->
    <select id="selectRecordsWithShopByOrderCode" resultMap="AccountTradeDetailResult">
        <include refid="selectAccountTradeDetailVo"/>
        where order_code = #{orderCode}
        and shop is not null and shop != ''
        and trade_type in
        <foreach collection="tradeTypes" item="tradeType" open="(" separator="," close=")">
            #{tradeType}
        </foreach>
        order by id asc
        limit 1
    </select>
</mapper>