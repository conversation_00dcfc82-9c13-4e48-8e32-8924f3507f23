package com.ruoyi.data.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购单-sku维度对象 purchase_order_sku_dim
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public class PurchaseOrderSkuDim extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private Long id;

    /** 采购单号 */
    @Excel(name = "采购单号")
    private String purchaseOrderNo;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date creationTime;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 付款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "付款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date completionTime;

    /** 状态 */
    @Excel(name = "状态")
    private String orderStatus;

    /** 创建类型 */
    @Excel(name = "创建类型")
    private String creationType;

    /** 来源单号 */
    @Excel(name = "来源单号")
    private String sourceOrderNo;

    /** 系统sku */
    @Excel(name = "系统sku")
    private String systemSku;

    /** 客户sku */
    @Excel(name = "客户sku")
    private String customerSku;

    /** 折前价 */
    @Excel(name = "折前价")
    private String originalPrice;

    /** 产品折扣 */
    @Excel(name = "产品折扣")
    private String productDiscount;

    /** 折后价 */
    @Excel(name = "折后价")
    private String discountedPrice;

    /** 入库数量 */
    @Excel(name = "入库数量")
    private String receivedQuantity;

    /** 正常采购 */
    @Excel(name = "正常采购")
    private String normalPurchase;

    /** 商户冗余 */
    @Excel(name = "商户冗余")
    private String merchantRedundancy;

    /** 取消数量 */
    @Excel(name = "取消数量")
    private String cancelledQuantity;

    /** 下单数量 */
    @Excel(name = "下单数量")
    private String orderQuantity;

    /** 未入库数 */
    @Excel(name = "未入库数")
    private String unreceivedQuantity;

    /** 采购员 */
    @Excel(name = "采购员")
    private String purchaser;

    /** 仓库CODE */
    @Excel(name = "仓库CODE")
    private String warehouseCode;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String warehouseName;

    /** 取消原因 */
    @Excel(name = "取消原因")
    private String cancelReason;

    /** 在途数 */
    @Excel(name = "在途数")
    private String inTransitQuantity;

    /** 作废原因 */
    @Excel(name = "作废原因")
    private String voidReason;

    /** 作废数量 */
    @Excel(name = "作废数量")
    private String voidQuantity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) 
    {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getPurchaseOrderNo() 
    {
        return purchaseOrderNo;
    }

    public void setCreationTime(Date creationTime) 
    {
        this.creationTime = creationTime;
    }

    public Date getCreationTime() 
    {
        return creationTime;
    }

    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }

    public void setPaymentTime(Date paymentTime) 
    {
        this.paymentTime = paymentTime;
    }

    public Date getPaymentTime() 
    {
        return paymentTime;
    }

    public void setCompletionTime(Date completionTime) 
    {
        this.completionTime = completionTime;
    }

    public Date getCompletionTime() 
    {
        return completionTime;
    }

    public void setOrderStatus(String orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatus() 
    {
        return orderStatus;
    }

    public void setCreationType(String creationType) 
    {
        this.creationType = creationType;
    }

    public String getCreationType() 
    {
        return creationType;
    }

    public void setSourceOrderNo(String sourceOrderNo) 
    {
        this.sourceOrderNo = sourceOrderNo;
    }

    public String getSourceOrderNo() 
    {
        return sourceOrderNo;
    }

    public void setSystemSku(String systemSku) 
    {
        this.systemSku = systemSku;
    }

    public String getSystemSku() 
    {
        return systemSku;
    }

    public void setCustomerSku(String customerSku) 
    {
        this.customerSku = customerSku;
    }

    public String getCustomerSku() 
    {
        return customerSku;
    }

    public void setOriginalPrice(String originalPrice) 
    {
        this.originalPrice = originalPrice;
    }

    public String getOriginalPrice() 
    {
        return originalPrice;
    }

    public void setProductDiscount(String productDiscount) 
    {
        this.productDiscount = productDiscount;
    }

    public String getProductDiscount() 
    {
        return productDiscount;
    }

    public void setDiscountedPrice(String discountedPrice) 
    {
        this.discountedPrice = discountedPrice;
    }

    public String getDiscountedPrice() 
    {
        return discountedPrice;
    }

    public void setReceivedQuantity(String receivedQuantity) 
    {
        this.receivedQuantity = receivedQuantity;
    }

    public String getReceivedQuantity() 
    {
        return receivedQuantity;
    }

    public void setNormalPurchase(String normalPurchase) 
    {
        this.normalPurchase = normalPurchase;
    }

    public String getNormalPurchase() 
    {
        return normalPurchase;
    }

    public void setMerchantRedundancy(String merchantRedundancy) 
    {
        this.merchantRedundancy = merchantRedundancy;
    }

    public String getMerchantRedundancy() 
    {
        return merchantRedundancy;
    }

    public void setCancelledQuantity(String cancelledQuantity) 
    {
        this.cancelledQuantity = cancelledQuantity;
    }

    public String getCancelledQuantity() 
    {
        return cancelledQuantity;
    }

    public void setOrderQuantity(String orderQuantity) 
    {
        this.orderQuantity = orderQuantity;
    }

    public String getOrderQuantity() 
    {
        return orderQuantity;
    }

    public void setUnreceivedQuantity(String unreceivedQuantity) 
    {
        this.unreceivedQuantity = unreceivedQuantity;
    }

    public String getUnreceivedQuantity() 
    {
        return unreceivedQuantity;
    }

    public void setPurchaser(String purchaser) 
    {
        this.purchaser = purchaser;
    }

    public String getPurchaser() 
    {
        return purchaser;
    }

    public void setWarehouseCode(String warehouseCode) 
    {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseCode() 
    {
        return warehouseCode;
    }

    public void setWarehouseName(String warehouseName) 
    {
        this.warehouseName = warehouseName;
    }

    public String getWarehouseName() 
    {
        return warehouseName;
    }

    public void setCancelReason(String cancelReason) 
    {
        this.cancelReason = cancelReason;
    }

    public String getCancelReason() 
    {
        return cancelReason;
    }

    public void setInTransitQuantity(String inTransitQuantity) 
    {
        this.inTransitQuantity = inTransitQuantity;
    }

    public String getInTransitQuantity() 
    {
        return inTransitQuantity;
    }

    public void setVoidReason(String voidReason) 
    {
        this.voidReason = voidReason;
    }

    public String getVoidReason() 
    {
        return voidReason;
    }

    public void setVoidQuantity(String voidQuantity) 
    {
        this.voidQuantity = voidQuantity;
    }

    public String getVoidQuantity() 
    {
        return voidQuantity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("purchaseOrderNo", getPurchaseOrderNo())
            .append("creationTime", getCreationTime())
            .append("auditTime", getAuditTime())
            .append("paymentTime", getPaymentTime())
            .append("completionTime", getCompletionTime())
            .append("orderStatus", getOrderStatus())
            .append("creationType", getCreationType())
            .append("sourceOrderNo", getSourceOrderNo())
            .append("systemSku", getSystemSku())
            .append("customerSku", getCustomerSku())
            .append("originalPrice", getOriginalPrice())
            .append("productDiscount", getProductDiscount())
            .append("discountedPrice", getDiscountedPrice())
            .append("receivedQuantity", getReceivedQuantity())
            .append("normalPurchase", getNormalPurchase())
            .append("merchantRedundancy", getMerchantRedundancy())
            .append("cancelledQuantity", getCancelledQuantity())
            .append("orderQuantity", getOrderQuantity())
            .append("unreceivedQuantity", getUnreceivedQuantity())
            .append("purchaser", getPurchaser())
            .append("warehouseCode", getWarehouseCode())
            .append("warehouseName", getWarehouseName())
            .append("cancelReason", getCancelReason())
            .append("inTransitQuantity", getInTransitQuantity())
            .append("voidReason", getVoidReason())
            .append("voidQuantity", getVoidQuantity())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
