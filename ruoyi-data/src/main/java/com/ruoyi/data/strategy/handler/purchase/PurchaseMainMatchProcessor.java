package com.ruoyi.data.strategy.handler.purchase;

import com.ruoyi.data.domain.OrderSkuDim;
import com.ruoyi.data.domain.PurchaseOrderDim;
import com.ruoyi.data.mapper.OrderSkuDimMapper;
import com.ruoyi.data.mapper.PurchaseOrderDimMapper;
import com.ruoyi.data.strategy.annotation.ProcessorConfig;
import com.ruoyi.data.strategy.core.AbstractTradeProcessor;
import com.ruoyi.data.strategy.dto.ProcessContext;
import com.ruoyi.data.strategy.dto.ProcessResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 采购单主匹配处理器
 * 处理逻辑：order_code → purchase_order_dim.purchase_order_no
 * 如果匹配到，获取source_order_no → order_sku_dim.package_no
 * 
 * <AUTHOR>
 */
@Component
@ProcessorConfig(
    tradeTypes = {"采购单"},
    order = 20,
    description = "采购单主匹配处理器，通过采购订单号匹配店铺信息",
    group = "matching"
)
public class PurchaseMainMatchProcessor extends AbstractTradeProcessor {
    
    @Autowired
    private PurchaseOrderDimMapper purchaseOrderDimMapper;
    
    @Autowired
    private OrderSkuDimMapper orderSkuDimMapper;
    
    @Override
    protected ProcessResult doProcess(ProcessContext context) {
        String orderCode = context.getOrderCode();
        String routeFlag = context.getRouteFlag();
        
        logger.info("采购单主匹配处理器开始处理，交易单号: {}，路由标记: {}", orderCode, routeFlag);
        
        // 检查路由标记，只处理主匹配流程
        if (!"MAIN_MATCH".equals(routeFlag)) {
            logger.debug("采购单主匹配处理器：路由标记不匹配，跳过处理，路由标记: {}", routeFlag);
            return createContinueResult("路由标记不匹配，跳过主匹配处理");
        }
        
        // 验证交易单号
        if (!StringUtils.hasText(orderCode)) {
            logger.warn("采购单主匹配处理器：交易单号为空");
            return createContinueResult("交易单号为空，无法进行采购单主匹配");
        }
        
        try {
            // 1. 用orderCode与purchase_order_dim.purchase_order_no匹配
            PurchaseOrderDim purchaseOrder = purchaseOrderDimMapper.selectByPurchaseOrderNo(orderCode);
            
            if (purchaseOrder == null) {
                logger.info("采购单主匹配处理器：交易单号在采购订单维度表中无匹配结果，交易单号: {}", orderCode);
                return createContinueResult("交易单号在采购订单维度表中无匹配结果");
            }
            
            logger.info("采购单主匹配处理器：找到采购订单记录，采购单号: {}，来源单号: {}", 
                       purchaseOrder.getPurchaseOrderNo(), purchaseOrder.getSourceOrderNo());
            
            // 2. 检查来源单号是否存在
            String sourceOrderNo = purchaseOrder.getSourceOrderNo();
            if (!StringUtils.hasText(sourceOrderNo)) {
                logger.warn("采购单主匹配处理器：采购订单记录中来源单号为空，采购单号: {}", 
                           purchaseOrder.getPurchaseOrderNo());
                return createContinueResult("采购订单记录中来源单号为空");
            }
            
            // 3. 用source_order_no与order_sku_dim.package_no匹配
            List<OrderSkuDim> orderSkuList = orderSkuDimMapper.selectByPackageNo(sourceOrderNo);
            
            if (CollectionUtils.isEmpty(orderSkuList)) {
                logger.info("采购单主匹配处理器：来源单号在订单SKU维度表中无匹配结果，来源单号: {}", sourceOrderNo);
                return createContinueResult("来源单号在订单SKU维度表中无匹配结果");
            }
            
            logger.info("采购单主匹配处理器：找到 {} 条订单SKU维度记录，来源单号: {}", orderSkuList.size(), sourceOrderNo);
            
            // 4. 收集所有店铺账号并去重
            Set<String> storeAccounts = orderSkuList.stream()
                    .map(OrderSkuDim::getStoreAccount)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toSet());
            
            if (storeAccounts.isEmpty()) {
                logger.warn("采购单主匹配处理器：订单SKU维度记录中无有效店铺账号，来源单号: {}", sourceOrderNo);
                return createContinueResult("订单SKU维度记录中店铺账号为空");
            }
            
            // 5. 检查匹配结果的唯一性
            if (storeAccounts.size() == 1) {
                String storeAccount = storeAccounts.iterator().next();
                String orderNo = orderSkuList.get(0).getOrderNo(); // 取第一个记录的订单号
                logger.info("采购单主匹配处理器：匹配成功，来源单号: {}，店铺账号: {}，订单号: {}", 
                           sourceOrderNo, storeAccount, orderNo);
                return createSuccessResult(storeAccount, orderNo);
            } else {
                // 多个匹配结果，完整记录所有店铺账号
                List<String> storeAccountList = new ArrayList<>(storeAccounts);
                logger.warn("采购单主匹配处理器：找到多个店铺账号，来源单号: {}，店铺账号: {}", 
                           sourceOrderNo, String.join(", ", storeAccountList));
                
                // 🔧 使用新的多结果警告方法，完整记录所有店铺信息
                return createMultipleResultWarning(storeAccountList);
            }
            
        } catch (Exception e) {
            logger.error("采购单主匹配处理器异常，交易单号: {}, 错误: {}", orderCode, e.getMessage(), e);
            return createContinueResult("采购单主匹配处理器异常: " + e.getMessage());
        }
    }
    
    @Override
    public String getProcessorName() {
        return "采购单主匹配处理器";
    }
} 