package com.ruoyi.data.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.data.mapper.PurchaseOrderSkuDimMapper;
import com.ruoyi.data.domain.PurchaseOrderSkuDim;
import com.ruoyi.data.service.IPurchaseOrderSkuDimService;

/**
 * 采购单-sku维度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class PurchaseOrderSkuDimServiceImpl implements IPurchaseOrderSkuDimService 
{
    @Autowired
    private PurchaseOrderSkuDimMapper purchaseOrderSkuDimMapper;

    /**
     * 查询采购单-sku维度
     * 
     * @param id 采购单-sku维度主键
     * @return 采购单-sku维度
     */
    @Override
    public PurchaseOrderSkuDim selectPurchaseOrderSkuDimById(Long id)
    {
        return purchaseOrderSkuDimMapper.selectPurchaseOrderSkuDimById(id);
    }

    /**
     * 查询采购单-sku维度列表
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 采购单-sku维度
     */
    @Override
    public List<PurchaseOrderSkuDim> selectPurchaseOrderSkuDimList(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return purchaseOrderSkuDimMapper.selectPurchaseOrderSkuDimList(purchaseOrderSkuDim);
    }

    /**
     * 新增采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    @Override
    public int insertPurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        purchaseOrderSkuDim.setCreateTime(DateUtils.getNowDate());
        return purchaseOrderSkuDimMapper.insertPurchaseOrderSkuDim(purchaseOrderSkuDim);
    }

    /**
     * 修改采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    @Override
    public int updatePurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        purchaseOrderSkuDim.setUpdateTime(DateUtils.getNowDate());
        return purchaseOrderSkuDimMapper.updatePurchaseOrderSkuDim(purchaseOrderSkuDim);
    }

    /**
     * 批量删除采购单-sku维度
     * 
     * @param ids 需要删除的采购单-sku维度主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderSkuDimByIds(Long[] ids)
    {
        return purchaseOrderSkuDimMapper.deletePurchaseOrderSkuDimByIds(ids);
    }

    /**
     * 删除采购单-sku维度信息
     * 
     * @param id 采购单-sku维度主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderSkuDimById(Long id)
    {
        return purchaseOrderSkuDimMapper.deletePurchaseOrderSkuDimById(id);
    }
}
