package com.ruoyi.data.service;

import java.util.List;
import com.ruoyi.data.domain.PlatformWarehouseStockOrder;
import com.ruoyi.data.util.PageQueryFunction;
import org.springframework.web.multipart.MultipartFile;

/**
 * 平台仓备货单（国内）Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IPlatformWarehouseStockOrderService 
{
    /**
     * 查询平台仓备货单（国内）
     * 
     * @param id 平台仓备货单（国内）主键
     * @return 平台仓备货单（国内）
     */
    public PlatformWarehouseStockOrder selectPlatformWarehouseStockOrderById(Long id);

    /**
     * 查询平台仓备货单（国内）列表
     * 
     * @param platformWarehouseStockOrder 平台仓备货单（国内）
     * @return 平台仓备货单（国内）集合
     */
    public List<PlatformWarehouseStockOrder> selectPlatformWarehouseStockOrderList(PlatformWarehouseStockOrder platformWarehouseStockOrder);

    /**
     * 分页查询平台仓备货单（国内）列表（用于流式导出全量数据）
     * 
     * @param platformWarehouseStockOrder 查询条件
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 平台仓备货单（国内）集合
     */
    public List<PlatformWarehouseStockOrder> selectPlatformWarehouseStockOrderListByPage(PlatformWarehouseStockOrder platformWarehouseStockOrder, int pageNum, int pageSize);

    /**
     * 统计平台仓备货单（国内）总数（用于导出进度计算）
     * 
     * @param platformWarehouseStockOrder 查询条件
     * @return 总数
     */
    public long countPlatformWarehouseStockOrder(PlatformWarehouseStockOrder platformWarehouseStockOrder);

    /**
     * 创建分页查询函数（用于流式导出）
     * 
     * @param platformWarehouseStockOrder 查询条件
     * @return 分页查询函数
     */
    public PageQueryFunction<PlatformWarehouseStockOrder> createPageQueryFunction(PlatformWarehouseStockOrder platformWarehouseStockOrder);

    /**
     * 新增平台仓备货单（国内）
     * 
     * @param platformWarehouseStockOrder 平台仓备货单（国内）
     * @return 结果
     */
    public int insertPlatformWarehouseStockOrder(PlatformWarehouseStockOrder platformWarehouseStockOrder);

    /**
     * 修改平台仓备货单（国内）
     * 
     * @param platformWarehouseStockOrder 平台仓备货单（国内）
     * @return 结果
     */
    public int updatePlatformWarehouseStockOrder(PlatformWarehouseStockOrder platformWarehouseStockOrder);

    /**
     * 批量删除平台仓备货单（国内）
     * 
     * @param ids 需要删除的平台仓备货单（国内）主键集合
     * @return 结果
     */
    public int deletePlatformWarehouseStockOrderByIds(Long[] ids);

    /**
     * 删除平台仓备货单（国内）信息
     * 
     * @param id 平台仓备货单（国内）主键
     * @return 结果
     */
    public int deletePlatformWarehouseStockOrderById(Long id);

    /**
     * 导入平台仓备货单（国内）数据
     *
     * @param file Excel文件
     * @return 导入结果
     */
    public String importPlatformWarehouseStockOrder(MultipartFile file) throws Exception;
}
