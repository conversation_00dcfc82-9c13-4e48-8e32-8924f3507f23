package com.ruoyi.data.mapper;

import java.util.List;
import com.ruoyi.data.domain.PurchaseOrderSkuDim;

/**
 * 采购单-sku维度Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface PurchaseOrderSkuDimMapper 
{
    /**
     * 查询采购单-sku维度
     * 
     * @param id 采购单-sku维度主键
     * @return 采购单-sku维度
     */
    public PurchaseOrderSkuDim selectPurchaseOrderSkuDimById(Long id);

    /**
     * 查询采购单-sku维度列表
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 采购单-sku维度集合
     */
    public List<PurchaseOrderSkuDim> selectPurchaseOrderSkuDimList(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 新增采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    public int insertPurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 修改采购单-sku维度
     * 
     * @param purchaseOrderSkuDim 采购单-sku维度
     * @return 结果
     */
    public int updatePurchaseOrderSkuDim(PurchaseOrderSkuDim purchaseOrderSkuDim);

    /**
     * 删除采购单-sku维度
     * 
     * @param id 采购单-sku维度主键
     * @return 结果
     */
    public int deletePurchaseOrderSkuDimById(Long id);

    /**
     * 批量删除采购单-sku维度
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePurchaseOrderSkuDimByIds(Long[] ids);
}
