package com.ruoyi.data.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Excel合并单元格预处理工具
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Component
public class MergedCellProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(MergedCellProcessor.class);
    
    /**
     * 处理Excel文件中的合并单元格，将其拆分并填充数据
     * 
     * @param file Excel文件
     * @return 处理后的标准格式数据
     * @throws Exception 处理异常
     */
    public ProcessResult processMergedCells(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("Excel文件不能为空");
        }
        
        log.info("开始处理Excel合并单元格，文件名：{}", file.getOriginalFilename());
        
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = createWorkbook(inputStream, file.getOriginalFilename());
            return processMergedCells(workbook);
        }
    }
    
    /**
     * 处理工作簿中的合并单元格
     * 
     * @param workbook Excel工作簿
     * @return 处理结果
     */
    public ProcessResult processMergedCells(Workbook workbook) throws Exception {
        ProcessResult result = new ProcessResult();
        
        // 默认处理第一个工作表
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) {
            throw new IllegalArgumentException("Excel文件中没有找到工作表");
        }
        
        log.info("处理工作表：{}，合并区域数量：{}", sheet.getSheetName(), sheet.getNumMergedRegions());
        
        // 1. 收集所有合并单元格信息
        List<CellRangeAddress> mergedRegions = collectMergedRegions(sheet);
        result.setMergedRegionCount(mergedRegions.size());
        
        // 2. 创建单元格值映射
        Map<String, Object> cellValueMap = createCellValueMap(sheet, mergedRegions);
        
        // 3. 生成标准化数据
        List<List<Object>> standardizedData = generateStandardizedData(sheet, cellValueMap);
        result.setProcessedData(standardizedData);
        
        // 4. 统计信息
        result.setTotalRows(standardizedData.size());
        result.setTotalColumns(standardizedData.isEmpty() ? 0 : standardizedData.get(0).size());
        result.setProcessedCellCount(calculateProcessedCells(mergedRegions));
        
        log.info("合并单元格处理完成，原始合并区域：{}，处理后行数：{}，列数：{}", 
                mergedRegions.size(), result.getTotalRows(), result.getTotalColumns());
        
        return result;
    }
    
    /**
     * 收集工作表中所有合并单元格区域
     */
    private List<CellRangeAddress> collectMergedRegions(Sheet sheet) {
        List<CellRangeAddress> mergedRegions = new ArrayList<>();
        
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            mergedRegions.add(mergedRegion);
            
            log.debug("发现合并区域：({}, {}) -> ({}, {})", 
                    mergedRegion.getFirstRow(), mergedRegion.getFirstColumn(),
                    mergedRegion.getLastRow(), mergedRegion.getLastColumn());
        }
        
        return mergedRegions;
    }
    
    /**
     * 创建单元格值映射，包括合并单元格的值填充
     */
    private Map<String, Object> createCellValueMap(Sheet sheet, List<CellRangeAddress> mergedRegions) {
        Map<String, Object> cellValueMap = new HashMap<>();
        
        // 1. 先读取所有普通单元格的值
        for (Row row : sheet) {
            if (row == null) continue;
            
            for (Cell cell : row) {
                if (cell == null) continue;
                
                String cellKey = getCellKey(row.getRowNum(), cell.getColumnIndex());
                Object cellValue = getCellValue(cell);
                if (cellValue != null) {
                    cellValueMap.put(cellKey, cellValue);
                }
            }
        }
        
        // 2. 处理合并单元格：将主单元格的值填充到整个合并区域
        for (CellRangeAddress mergedRegion : mergedRegions) {
            // 获取合并区域的主单元格值（左上角单元格）
            String masterCellKey = getCellKey(mergedRegion.getFirstRow(), mergedRegion.getFirstColumn());
            Object masterValue = cellValueMap.get(masterCellKey);
            
            if (masterValue != null && !isEmptyValue(masterValue)) {
                // 将主单元格的值填充到整个合并区域
                for (int row = mergedRegion.getFirstRow(); row <= mergedRegion.getLastRow(); row++) {
                    for (int col = mergedRegion.getFirstColumn(); col <= mergedRegion.getLastColumn(); col++) {
                        String cellKey = getCellKey(row, col);
                        cellValueMap.put(cellKey, masterValue);
                    }
                }
                
                log.debug("合并单元格值填充：区域({}, {}) -> ({}, {})，值：{}", 
                        mergedRegion.getFirstRow(), mergedRegion.getFirstColumn(),
                        mergedRegion.getLastRow(), mergedRegion.getLastColumn(), masterValue);
            }
        }
        
        return cellValueMap;
    }
    
    /**
     * 生成标准化数据（每行每列都有完整数据）
     */
    private List<List<Object>> generateStandardizedData(Sheet sheet, Map<String, Object> cellValueMap) {
        List<List<Object>> result = new ArrayList<>();
        
        // 确定数据范围
        int maxRow = sheet.getLastRowNum();
        int maxCol = getMaxColumnIndex(sheet);
        
        log.debug("数据范围：最大行 {}，最大列 {}", maxRow, maxCol);
        
        // 生成标准化数据
        for (int rowIndex = 0; rowIndex <= maxRow; rowIndex++) {
            List<Object> rowData = new ArrayList<>();
            
            for (int colIndex = 0; colIndex <= maxCol; colIndex++) {
                String cellKey = getCellKey(rowIndex, colIndex);
                Object cellValue = cellValueMap.get(cellKey);
                
                // 如果单元格值为空，使用空字符串
                rowData.add(cellValue != null ? cellValue : "");
            }
            
            result.add(rowData);
        }
        
        return result;
    }
    
    /**
     * 获取工作表的最大列索引
     */
    private int getMaxColumnIndex(Sheet sheet) {
        int maxCol = 0;
        
        for (Row row : sheet) {
            if (row != null && row.getLastCellNum() > maxCol) {
                maxCol = row.getLastCellNum() - 1; // getLastCellNum()返回的是列数，需要减1得到索引
            }
        }
        
        return maxCol;
    }
    
    /**
     * 获取单元格的值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 统一时间格式处理 - 始终返回格式化的字符串
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(cell.getDateCellValue());
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    try {
                        return cell.getNumericCellValue();
                    } catch (Exception ex) {
                        return cell.toString();
                    }
                }
            case BLANK:
                return null;
            default:
                return cell.toString();
        }
    }
    
    /**
     * 生成单元格键（行_列）
     */
    private String getCellKey(int row, int col) {
        return row + "_" + col;
    }
    
    /**
     * 判断值是否为空
     */
    private boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        return false;
    }
    
    /**
     * 计算处理的单元格数量
     */
    private int calculateProcessedCells(List<CellRangeAddress> mergedRegions) {
        int count = 0;
        for (CellRangeAddress region : mergedRegions) {
            int rows = region.getLastRow() - region.getFirstRow() + 1;
            int cols = region.getLastColumn() - region.getFirstColumn() + 1;
            count += (rows * cols) - 1; // 减去主单元格，因为它本来就有值
        }
        return count;
    }
    
    /**
     * 根据文件名创建对应的工作簿对象
     */
    private Workbook createWorkbook(InputStream inputStream, String fileName) throws Exception {
        if (fileName == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        String fileExtension = getFileExtension(fileName);
        
        switch (fileExtension.toLowerCase()) {
            case "xlsx":
                return new XSSFWorkbook(inputStream);
            case "xls":
                return new HSSFWorkbook(inputStream);
            default:
                throw new IllegalArgumentException("不支持的文件格式：" + fileExtension + "，仅支持.xls和.xlsx文件");
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1) {
            throw new IllegalArgumentException("文件名必须包含扩展名");
        }
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * 处理结果类
     */
    public static class ProcessResult {
        private List<List<Object>> processedData;
        private int mergedRegionCount;
        private int processedCellCount;
        private int totalRows;
        private int totalColumns;
        
        // Getters and Setters
        public List<List<Object>> getProcessedData() {
            return processedData;
        }
        
        public void setProcessedData(List<List<Object>> processedData) {
            this.processedData = processedData;
        }
        
        public int getMergedRegionCount() {
            return mergedRegionCount;
        }
        
        public void setMergedRegionCount(int mergedRegionCount) {
            this.mergedRegionCount = mergedRegionCount;
        }
        
        public int getProcessedCellCount() {
            return processedCellCount;
        }
        
        public void setProcessedCellCount(int processedCellCount) {
            this.processedCellCount = processedCellCount;
        }
        
        public int getTotalRows() {
            return totalRows;
        }
        
        public void setTotalRows(int totalRows) {
            this.totalRows = totalRows;
        }
        
        public int getTotalColumns() {
            return totalColumns;
        }
        
        public void setTotalColumns(int totalColumns) {
            this.totalColumns = totalColumns;
        }
        
        public boolean hasMergedCells() {
            return mergedRegionCount > 0;
        }
        
        @Override
        public String toString() {
            return String.format("ProcessResult{合并区域数量=%d, 处理单元格数=%d, 总行数=%d, 总列数=%d}", 
                    mergedRegionCount, processedCellCount, totalRows, totalColumns);
        }
    }
} 