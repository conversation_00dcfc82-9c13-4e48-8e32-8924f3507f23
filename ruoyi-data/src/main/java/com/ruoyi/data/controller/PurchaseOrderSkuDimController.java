package com.ruoyi.data.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.data.domain.PurchaseOrderSkuDim;
import com.ruoyi.data.service.IPurchaseOrderSkuDimService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 采购单-sku维度Controller
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@RestController
@RequestMapping("/data/purchaseOrderSkuDim")
public class PurchaseOrderSkuDimController extends BaseController
{
    @Autowired
    private IPurchaseOrderSkuDimService purchaseOrderSkuDimService;

    /**
     * 查询采购单-sku维度列表
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:list')")
    @GetMapping("/list")
    public TableDataInfo list(PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        startPage();
        List<PurchaseOrderSkuDim> list = purchaseOrderSkuDimService.selectPurchaseOrderSkuDimList(purchaseOrderSkuDim);
        return getDataTable(list);
    }

    /**
     * 导出采购单-sku维度列表
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:export')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        List<PurchaseOrderSkuDim> list = purchaseOrderSkuDimService.selectPurchaseOrderSkuDimList(purchaseOrderSkuDim);
        ExcelUtil<PurchaseOrderSkuDim> util = new ExcelUtil<PurchaseOrderSkuDim>(PurchaseOrderSkuDim.class);
        util.exportExcel(response, list, "采购单-sku维度数据");
    }

    /**
     * 获取采购单-sku维度详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(purchaseOrderSkuDimService.selectPurchaseOrderSkuDimById(id));
    }

    /**
     * 新增采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:add')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return toAjax(purchaseOrderSkuDimService.insertPurchaseOrderSkuDim(purchaseOrderSkuDim));
    }

    /**
     * 修改采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:edit')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PurchaseOrderSkuDim purchaseOrderSkuDim)
    {
        return toAjax(purchaseOrderSkuDimService.updatePurchaseOrderSkuDim(purchaseOrderSkuDim));
    }

    /**
     * 删除采购单-sku维度
     */
    @PreAuthorize("@ss.hasPermi('data:purchaseOrderSkuDim:remove')")
    @Log(title = "采购单-sku维度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(purchaseOrderSkuDimService.deletePurchaseOrderSkuDimByIds(ids));
    }
}
