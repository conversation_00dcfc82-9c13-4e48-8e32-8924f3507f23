<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="采购单号" prop="purchaseOrderNo">
        <el-input
          v-model="queryParams.purchaseOrderNo"
          placeholder="请输入采购单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="creationTime">
        <el-date-picker clearable
          v-model="queryParams.creationTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker clearable
          v-model="queryParams.auditTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审核时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="付款时间" prop="paymentTime">
        <el-date-picker clearable
          v-model="queryParams.paymentTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择付款时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="完成时间" prop="completionTime">
        <el-date-picker clearable
          v-model="queryParams.completionTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择完成时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="来源单号" prop="sourceOrderNo">
        <el-input
          v-model="queryParams.sourceOrderNo"
          placeholder="请输入来源单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="系统sku" prop="systemSku">
        <el-input
          v-model="queryParams.systemSku"
          placeholder="请输入系统sku"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户sku" prop="customerSku">
        <el-input
          v-model="queryParams.customerSku"
          placeholder="请输入客户sku"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购员" prop="purchaser">
        <el-input
          v-model="queryParams.purchaser"
          placeholder="请输入采购员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="仓库CODE" prop="warehouseCode">
        <el-input
          v-model="queryParams.warehouseCode"
          placeholder="请输入仓库CODE"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入仓库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['data:purchaseOrderSkuDim:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['data:purchaseOrderSkuDim:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['data:purchaseOrderSkuDim:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['data:purchaseOrderSkuDim:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purchaseOrderSkuDimList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="自增ID" align="center" prop="id" />
      <el-table-column label="采购单号" align="center" prop="purchaseOrderNo" />
      <el-table-column label="创建时间" align="center" prop="creationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="付款时间" align="center" prop="paymentTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="completionTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completionTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="orderStatus" />
      <el-table-column label="创建类型" align="center" prop="creationType" />
      <el-table-column label="来源单号" align="center" prop="sourceOrderNo" />
      <el-table-column label="系统sku" align="center" prop="systemSku" />
      <el-table-column label="客户sku" align="center" prop="customerSku" />
      <el-table-column label="折前价" align="center" prop="originalPrice" />
      <el-table-column label="产品折扣" align="center" prop="productDiscount" />
      <el-table-column label="折后价" align="center" prop="discountedPrice" />
      <el-table-column label="入库数量" align="center" prop="receivedQuantity" />
      <el-table-column label="正常采购" align="center" prop="normalPurchase" />
      <el-table-column label="商户冗余" align="center" prop="merchantRedundancy" />
      <el-table-column label="取消数量" align="center" prop="cancelledQuantity" />
      <el-table-column label="下单数量" align="center" prop="orderQuantity" />
      <el-table-column label="未入库数" align="center" prop="unreceivedQuantity" />
      <el-table-column label="采购员" align="center" prop="purchaser" />
      <el-table-column label="仓库CODE" align="center" prop="warehouseCode" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="取消原因" align="center" prop="cancelReason" />
      <el-table-column label="在途数" align="center" prop="inTransitQuantity" />
      <el-table-column label="作废原因" align="center" prop="voidReason" />
      <el-table-column label="作废数量" align="center" prop="voidQuantity" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:purchaseOrderSkuDim:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['data:purchaseOrderSkuDim:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购单-sku维度对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采购单号" prop="purchaseOrderNo">
          <el-input v-model="form.purchaseOrderNo" placeholder="请输入采购单号" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creationTime">
          <el-date-picker clearable
            v-model="form.creationTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审核时间" prop="auditTime">
          <el-date-picker clearable
            v-model="form.auditTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审核时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="付款时间" prop="paymentTime">
          <el-date-picker clearable
            v-model="form.paymentTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择付款时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完成时间" prop="completionTime">
          <el-date-picker clearable
            v-model="form.completionTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="来源单号" prop="sourceOrderNo">
          <el-input v-model="form.sourceOrderNo" placeholder="请输入来源单号" />
        </el-form-item>
        <el-form-item label="系统sku" prop="systemSku">
          <el-input v-model="form.systemSku" placeholder="请输入系统sku" />
        </el-form-item>
        <el-form-item label="客户sku" prop="customerSku">
          <el-input v-model="form.customerSku" placeholder="请输入客户sku" />
        </el-form-item>
        <el-form-item label="折前价" prop="originalPrice">
          <el-input v-model="form.originalPrice" placeholder="请输入折前价" />
        </el-form-item>
        <el-form-item label="产品折扣" prop="productDiscount">
          <el-input v-model="form.productDiscount" placeholder="请输入产品折扣" />
        </el-form-item>
        <el-form-item label="折后价" prop="discountedPrice">
          <el-input v-model="form.discountedPrice" placeholder="请输入折后价" />
        </el-form-item>
        <el-form-item label="入库数量" prop="receivedQuantity">
          <el-input v-model="form.receivedQuantity" placeholder="请输入入库数量" />
        </el-form-item>
        <el-form-item label="正常采购" prop="normalPurchase">
          <el-input v-model="form.normalPurchase" placeholder="请输入正常采购" />
        </el-form-item>
        <el-form-item label="商户冗余" prop="merchantRedundancy">
          <el-input v-model="form.merchantRedundancy" placeholder="请输入商户冗余" />
        </el-form-item>
        <el-form-item label="取消数量" prop="cancelledQuantity">
          <el-input v-model="form.cancelledQuantity" placeholder="请输入取消数量" />
        </el-form-item>
        <el-form-item label="下单数量" prop="orderQuantity">
          <el-input v-model="form.orderQuantity" placeholder="请输入下单数量" />
        </el-form-item>
        <el-form-item label="未入库数" prop="unreceivedQuantity">
          <el-input v-model="form.unreceivedQuantity" placeholder="请输入未入库数" />
        </el-form-item>
        <el-form-item label="采购员" prop="purchaser">
          <el-input v-model="form.purchaser" placeholder="请输入采购员" />
        </el-form-item>
        <el-form-item label="仓库CODE" prop="warehouseCode">
          <el-input v-model="form.warehouseCode" placeholder="请输入仓库CODE" />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseName">
          <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
        </el-form-item>
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input v-model="form.cancelReason" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="在途数" prop="inTransitQuantity">
          <el-input v-model="form.inTransitQuantity" placeholder="请输入在途数" />
        </el-form-item>
        <el-form-item label="作废原因" prop="voidReason">
          <el-input v-model="form.voidReason" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="作废数量" prop="voidQuantity">
          <el-input v-model="form.voidQuantity" placeholder="请输入作废数量" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurchaseOrderSkuDim, getPurchaseOrderSkuDim, delPurchaseOrderSkuDim, addPurchaseOrderSkuDim, updatePurchaseOrderSkuDim } from "@/api/data/purchaseOrderSkuDim"

export default {
  name: "PurchaseOrderSkuDim",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购单-sku维度表格数据
      purchaseOrderSkuDimList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseOrderNo: null,
        creationTime: null,
        auditTime: null,
        paymentTime: null,
        completionTime: null,
        orderStatus: null,
        creationType: null,
        sourceOrderNo: null,
        systemSku: null,
        customerSku: null,
        purchaser: null,
        warehouseCode: null,
        warehouseName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询采购单-sku维度列表 */
    getList() {
      this.loading = true
      listPurchaseOrderSkuDim(this.queryParams).then(response => {
        this.purchaseOrderSkuDimList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        purchaseOrderNo: null,
        creationTime: null,
        auditTime: null,
        paymentTime: null,
        completionTime: null,
        orderStatus: null,
        creationType: null,
        sourceOrderNo: null,
        systemSku: null,
        customerSku: null,
        originalPrice: null,
        productDiscount: null,
        discountedPrice: null,
        receivedQuantity: null,
        normalPurchase: null,
        merchantRedundancy: null,
        cancelledQuantity: null,
        orderQuantity: null,
        unreceivedQuantity: null,
        purchaser: null,
        warehouseCode: null,
        warehouseName: null,
        cancelReason: null,
        inTransitQuantity: null,
        voidReason: null,
        voidQuantity: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加采购单-sku维度"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getPurchaseOrderSkuDim(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改采购单-sku维度"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePurchaseOrderSkuDim(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addPurchaseOrderSkuDim(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除采购单-sku维度编号为"' + ids + '"的数据项？').then(function() {
        return delPurchaseOrderSkuDim(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('data/purchaseOrderSkuDim/export', {
        ...this.queryParams
      }, `purchaseOrderSkuDim_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
