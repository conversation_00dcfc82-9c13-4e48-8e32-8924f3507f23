<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="需求标题" prop="trackTitle">
        <el-input
          v-model="queryParams.trackTitle"
          placeholder="请输入需求标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.req_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['requirement:demand:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['requirement:demand:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['requirement:demand:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['requirement:demand:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-document"
          size="mini"
          :disabled="single"
          @click="handleGenerate"
          v-hasPermi="['requirement:demand:download']"
        >生成文档</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="demandList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="需求标题" align="center" prop="trackTitle" />
      <el-table-column label="需求概述" align="center" prop="trackSummary" :show-overflow-tooltip="true" />
      <el-table-column label="技术栈" align="center" prop="techStack" :show-overflow-tooltip="true" />
      <el-table-column label="优先级" align="center" prop="priority">
        <template slot-scope="scope">
          <el-tag :type="getPriorityTag(scope.row.priority)">{{ scope.row.priority || '中等' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.req_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['requirement:demand:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleSteps(scope.row)"
            v-hasPermi="['requirement:demand:query']"
          >步骤</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document-copy"
            @click="handlePreview(scope.row)"
            v-hasPermi="['requirement:demand:query']"
          >预览文档</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['requirement:demand:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="handleGenerate(scope.row)"
            v-hasPermi="['requirement:demand:download']"
          >生成文档</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-notebook-1"
            @click="handleViewLogs(scope.row)"
            v-hasPermi="['requirement:demand:query']"
          >日志记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改需求对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="需求标题" prop="trackTitle">
          <el-input v-model="form.trackTitle" placeholder="请输入需求标题" />
        </el-form-item>
        <el-form-item label="需求概述" prop="trackSummary">
          <el-input v-model="form.trackSummary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="技术栈" prop="techStack">
          <el-input v-model="form.techStack" placeholder="请输入技术栈" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="form.priority" placeholder="请选择优先级">
            <el-option label="高" value="high" />
            <el-option label="中等" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="技术方案" prop="solutionContent">
          <el-input v-model="form.solutionContent" type="textarea" :rows="3" placeholder="技术方案由各步骤方案组成" :disabled="true" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择">
            <el-option
              v-for="dict in dict.type.req_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 需求文档预览对话框 -->
    <el-dialog title="需求文档预览" :visible.sync="previewVisible" width="80%" :close-on-click-modal="false" append-to-body>
      <div v-loading="previewLoading" class="document-preview">
        <!-- 需求标题 -->
        <div class="document-title">
          <h1>{{ previewData.title }}</h1>
        </div>
        
        <!-- 需求概述 -->
        <div class="document-section">
          <h2>需求概述</h2>
          <div class="document-content">{{ previewData.summary }}</div>
        </div>
        
        <!-- 技术栈 -->
        <div class="document-section" v-if="previewData.techStack">
          <h2>技术栈</h2>
          <div class="document-content">{{ previewData.techStack }}</div>
        </div>
        
        <!-- 需求步骤 -->
        <div class="document-section">
          <h2>需求步骤</h2>
          <div v-for="(step, index) in previewData.steps" :key="step.stepId" class="step-item">
            <h3>{{ index + 1 }}. {{ step.stepTitle }}</h3>
            <div class="step-content">{{ step.stepDesc }}</div>
            
            <!-- 步骤技术方案 -->
            <div class="step-tech-solution" v-if="step.techSolution">
              <h4>技术方案</h4>
              <div class="step-content">{{ step.techSolution }}</div>
            </div>
            
            <!-- 步骤图片 -->
            <div class="step-images" v-if="step.images && step.images.length > 0">
              <h4>相关图片</h4>
              <div class="image-list">
                <div v-for="image in step.images" :key="image.imageId" class="image-item">
                  <div class="image-container">
                    <img :src="image.imageUrl || (image.imagePath ? getImageUrl(image.imagePath) : '')" 
                         :alt="image.imageName" 
                         @click="previewImage(image.imageUrl || (image.imagePath ? getImageUrl(image.imagePath) : ''))" />
                  </div>
                  <div class="image-name">{{ image.imageName }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="exportAsPdf">导出为PDF</el-button>
        <el-button @click="previewVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 图片预览 -->
    <el-image-viewer 
      v-if="previewImageVisible" 
      :url-list="previewImageList" 
      :on-close="closeViewer" 
      :z-index="2000" />

    <!-- 需求日志对话框 -->
    <el-dialog :title="reqName + ' - 日志记录'" :visible.sync="logOpen" width="1200px" append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAddLog"
            v-hasPermi="['requirement:demand:edit']"
          >新增日志</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="logSingle"
            @click="handleUpdateLog"
            v-hasPermi="['requirement:demand:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="logMultiple"
            @click="handleDeleteLog"
            v-hasPermi="['requirement:demand:edit']"
          >删除</el-button>
        </el-col>
      </el-row>

      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="列表视图" name="list">
          <el-table v-loading="logLoading" :data="logList" @selection-change="handleLogSelectionChange" height="500">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="日期" align="center" prop="logDate" width="120">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.logDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="内容" align="left" prop="content" />
            <el-table-column label="操作人" align="center" prop="operator" width="120" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdateLog(scope.row)"
                  v-hasPermi="['requirement:demand:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteLog(scope.row)"
                  v-hasPermi="['requirement:demand:edit']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="日历视图" name="calendar">
          <el-calendar v-loading="logLoading">
            <template slot="dateCell" slot-scope="{date, data}">
              <div class="calendar-day">
                {{ data.day.split('-').slice(2).join('') }}
                <div v-for="log in getLogsByDate(date)" :key="log.logId" class="log-item" @click="showLogDetail(log)">
                  <el-tooltip :content="log.content" placement="top">
                    <div class="log-item-content">{{ log.content | ellipsis }}</div>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-calendar>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 添加或修改日志对话框 -->
    <el-dialog :title="logTitle" :visible.sync="logFormOpen" width="600px" append-to-body>
      <el-form ref="logForm" :model="logForm" :rules="logRules" label-width="100px">
        <el-form-item label="日志日期" prop="logDate">
          <el-date-picker clearable
            v-model="logForm.logDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日志日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="日志内容" prop="content">
          <el-input v-model="logForm.content" type="textarea" :rows="5" placeholder="请输入日志内容" />
        </el-form-item>
        <el-form-item label="操作人" prop="operator">
          <el-input v-model="logForm.operator" placeholder="请输入操作人" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitLogForm">确 定</el-button>
        <el-button @click="cancelLog">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="logDetailOpen" width="600px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="日期">{{ parseTime(currentLog.logDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="内容" :contentStyle="{ whiteSpace: 'pre-wrap' }">{{ currentLog.content }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ currentLog.operator }}</el-descriptions-item>
        <el-descriptions-item label="记录时间">{{ parseTime(currentLog.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(currentLog.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="logDetailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDemand, getDemand, addDemand, updateDemand, delDemand, generateFile } from "@/api/requirement/demand";
import { getStepsByTrackId } from "@/api/requirement/step";
import { getImagesByStepId } from "@/api/requirement/image";
import { getLogsByTrackId, getLog, addLog, updateLog, delLog } from "@/api/requirement/log";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: "Demand",
  dicts: ['req_status'],
  components: {
    ElImageViewer
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 需求跟踪表格数据
      demandList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        trackTitle: null,
        status: null
      },
      // 表单参数
      form: {
        trackId: null,
        trackTitle: null,
        trackSummary: null,
        techStack: null,
        solutionContent: null,
        status: "0",
        priority: "medium",
        remark: null
      },
      // 表单校验
      rules: {
        trackTitle: [
          { required: true, message: "需求标题不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      // 文档预览相关
      previewVisible: false,
      previewLoading: false,
      previewData: {
        title: '',
        summary: '',
        techStack: '',
        solution: '',
        steps: []
      },
      
      // 图片预览相关
      previewImageVisible: false,
      previewImageList: [],
      
      // 日志相关
      logOpen: false,
      logFormOpen: false,
      logDetailOpen: false,
      logTitle: "",
      reqName: "",
      currentTrackId: null,
      logLoading: false,
      logIds: [],
      logSingle: true,
      logMultiple: true,
      activeTab: "list",
      logList: [],
      currentLog: {},
      logForm: {},
      logRules: {
        logDate: [
          { required: true, message: "日志日期不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "日志内容不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询需求跟踪列表 */
    getList() {
      this.loading = true;
      listDemand(this.queryParams).then(response => {
        this.demandList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        trackId: null,
        trackTitle: null,
        trackSummary: null,
        techStack: null,
        solutionContent: null,
        status: "0",
        priority: "medium",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.trackId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加需求";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const trackId = row.trackId || this.ids[0]
      getDemand(trackId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改需求";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.trackId != null) {
            updateDemand(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDemand(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const trackIds = row.trackId || this.ids;
      this.$modal.confirm('是否确认删除需求跟踪编号为"' + trackIds + '"的数据项？').then(function() {
        return delDemand(trackIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看步骤 */
    handleSteps(row) {
      const trackId = row.trackId
      this.$router.push({ 
        path: '/requirement/step',
        query: {
          trackId: trackId,
          reqName: row.trackTitle
        }
      })
    },
    /** 预览文档 */
    handlePreview(row) {
      this.previewVisible = true;
      this.previewLoading = true;
      
      // 获取需求详情
      getDemand(row.trackId).then(response => {
        const reqData = response.data;
        
        this.previewData = {
          title: reqData.trackTitle,
          summary: reqData.trackSummary,
          techStack: reqData.techStack,
          solution: '', // 不再需要合并步骤技术方案
          steps: []
        };
        
        // 获取所有步骤
        getStepsByTrackId(row.trackId).then(stepsResponse => {
          const steps = stepsResponse.data || [];
          
          // 按步骤次序排序
          steps.sort((a, b) => a.stepOrder - b.stepOrder);
          
          // 计数器，记录已经加载的步骤数
          let loadedStepCount = 0;
          
          // 如果没有步骤，直接完成加载
          if (steps.length === 0) {
            this.previewLoading = false;
            return;
          }
          
          // 不再需要合并步骤技术方案
          
          // 遍历步骤，获取每个步骤的图片
          steps.forEach(step => {
            const stepData = {
              stepId: step.stepId,
              stepTitle: step.stepTitle,
              stepDesc: step.stepDesc,
              stepOrder: step.stepOrder,
              techSolution: step.techSolution,
              images: []
            };
            
            // 获取步骤图片
            getImagesByStepId(step.stepId).then(imagesResponse => {
              if (imagesResponse.code === 200 && imagesResponse.data) {
                stepData.images = imagesResponse.data;
              }
              
              // 添加到步骤列表
              this.previewData.steps.push(stepData);
              
              // 计数器加1
              loadedStepCount++;
              
              // 如果所有步骤都加载完成，关闭加载状态
              if (loadedStepCount === steps.length) {
                // 再次按照步骤次序排序
                this.previewData.steps.sort((a, b) => a.stepOrder - b.stepOrder);
                
                this.previewLoading = false;
              }
            }).catch(() => {
              // 即使获取图片失败，也计入已加载步骤
              loadedStepCount++;
              this.previewData.steps.push(stepData);
              
              if (loadedStepCount === steps.length) {
                this.previewData.steps.sort((a, b) => a.stepOrder - b.stepOrder);
                
                this.previewLoading = false;
              }
            });
          });
        }).catch(() => {
          this.previewLoading = false;
        });
      }).catch(() => {
        this.previewLoading = false;
      });
    },
    
    /** 获取图片URL */
    getImageUrl(imagePath) {
      // 如果是OSS路径
      if (imagePath && imagePath.startsWith('requirement/images/')) {
        return process.env.VUE_APP_BASE_API + '/profile/' + imagePath;
      } 
      // 如果是本地路径
      else if (imagePath) {
        const profileIndex = imagePath.indexOf('/requirement/images');
        if (profileIndex !== -1) {
          return process.env.VUE_APP_BASE_API + '/profile/requirement/images' + imagePath.substring(imagePath.lastIndexOf('/'));
        } else {
          return process.env.VUE_APP_BASE_API + '/profile' + imagePath;
        }
      }
      return '';
    },
    
    /** 预览图片 */
    previewImage(imageUrl) {
      if (!imageUrl) return;
      
      this.previewImageList = [imageUrl];
      this.previewImageVisible = true;
    },
    
    /** 关闭图片预览器 */
    closeViewer() {
      this.previewImageVisible = false;
    },
    
    /** 导出为PDF */
    exportAsPdf() {
      this.$modal.msgWarning("PDF导出功能尚在开发中");
      // 这部分可以使用html2canvas和jsPDF等库实现
      // 由于需要额外引入库，这里只做提示，实际实现需要额外的库
    },
    /** 文档生成按钮操作 */
    handleGenerate(row) {
      this.$modal.confirm('确认生成需求文档吗？').then(function() {
        return generateFile(row.trackId);
      }).then(response => {
        // 处理文件下载
        const blob = new Blob([response], { type: 'application/zip' });
        const link = document.createElement('a');
        const fileName = row.trackTitle + '_需求文档.zip';
        
        // 创建下载链接
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        
        // 触发下载
        link.click();
        
        // 清理
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
        
        this.$modal.msgSuccess("文档生成成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('requirement/demand/export', {
        ...this.queryParams
      }, `需求跟踪_${new Date().getTime()}.xlsx`)
    },
    /** 获取优先级对应的标签类型 */
    getPriorityTag(priority) {
      if (priority === 'high') {
        return 'danger';
      } else if (priority === 'low') {
        return 'info';
      } else {
        return 'warning';
      }
    },
    // 显示日志详情
    showLogDetail(log) {
      this.currentLog = log;
      this.logDetailOpen = true;
    },
    /** 查看需求日志按钮操作 */
    handleViewLogs(row) {
      this.logLoading = true;
      this.reqName = row.trackTitle || `需求 ${row.trackId}`;
      this.currentTrackId = row.trackId;
      this.logOpen = true;
      this.activeTab = "list";
      
      getLogsByTrackId(row.trackId).then(response => {
        this.logList = response.data || [];
        this.logLoading = false;
      });
    },
    // 多选框选中日志数据
    handleLogSelectionChange(selection) {
      this.logIds = selection.map(item => item.logId);
      this.logSingle = selection.length !== 1;
      this.logMultiple = !selection.length;
    },
    /** 新增日志按钮操作 */
    handleAddLog() {
      this.resetLogForm();
      this.logFormOpen = true;
      this.logTitle = "添加日志";
    },
    /** 修改日志按钮操作 */
    handleUpdateLog(row) {
      this.resetLogForm();
      const logId = row.logId || this.logIds[0];
      getLog(logId).then(response => {
        this.logForm = response.data;
        this.logFormOpen = true;
        this.logTitle = "修改日志";
      });
    },
    /** 删除日志按钮操作 */
    handleDeleteLog(row) {
      const logIds = row.logId || this.logIds;
      this.$modal.confirm('是否确认删除日志编号为"' + logIds + '"的数据项？').then(function() {
        return delLog(logIds);
      }).then(() => {
        // 重新加载日志列表
        getLogsByTrackId(this.currentTrackId).then(response => {
          this.logList = response.data || [];
        });
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 日志表单重置
    resetLogForm() {
      this.logForm = {
        logId: null,
        trackId: this.currentTrackId,
        logDate: this.parseTime(new Date(), '{y}-{m}-{d}'),
        content: null,
        operator: this.$store.getters.name || null
      };
      this.resetForm("logForm");
    },
    // 取消日志按钮
    cancelLog() {
      this.logFormOpen = false;
      this.resetLogForm();
    },
    // 提交日志表单
    submitLogForm() {
      this.$refs["logForm"].validate(valid => {
        if (valid) {
          if (this.logForm.logId != null) {
            updateLog(this.logForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.logFormOpen = false;
              // 重新加载日志列表
              getLogsByTrackId(this.currentTrackId).then(response => {
                this.logList = response.data || [];
              });
            });
          } else {
            addLog(this.logForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.logFormOpen = false;
              // 重新加载日志列表
              getLogsByTrackId(this.currentTrackId).then(response => {
                this.logList = response.data || [];
              });
            });
          }
        }
      });
    },
    // 根据日期获取日志
    getLogsByDate(date) {
      const dateString = this.parseTime(date, '{y}-{m}-{d}');
      return this.logList.filter(log => 
        this.parseTime(log.logDate, '{y}-{m}-{d}') === dateString
      );
    }
  },
  filters: {
    ellipsis(value) {
      if (!value) return '';
      if (value.length > 15) {
        return value.slice(0, 15) + '...';
      }
      return value;
    }
  }
};
</script>

<style scoped>
.document-preview {
  padding: 20px;
  background: #fff;
  color: #333;
  font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
  line-height: 1.6;
}

.document-title {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #eaeaea;
  padding-bottom: 15px;
}

.document-title h1 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.document-section {
  margin-bottom: 25px;
}

.document-section h2 {
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 8px;
  margin-bottom: 15px;
  color: #409EFF;
}

.document-content {
  text-indent: 2em;
  margin-bottom: 15px;
  white-space: pre-wrap;
}

.step-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eaeaea;
}

.step-item h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #555;
}

.step-content {
  margin-bottom: 15px;
  text-indent: 2em;
  white-space: pre-wrap;
}

.step-tech-solution h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
}

.step-tech-solution .step-content {
  margin-bottom: 15px;
  text-indent: 2em;
  white-space: pre-wrap;
}

.step-images h4 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #666;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.image-item {
  width: 25%;
  padding: 10px;
  box-sizing: border-box;
}

.image-container {
  height: 180px;
  border: 1px solid #eaeaea;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #f7f7f7;
  cursor: pointer;
}

.image-container img {
  max-width: 100%;
  max-height: 160px;
  object-fit: contain;
  transition: transform 0.3s;
}

.image-container img:hover {
  transform: scale(1.05);
}

.image-name {
  text-align: center;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (max-width: 1200px) {
  .image-item {
    width: 33.33%;
  }
}

@media (max-width: 992px) {
  .image-item {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .image-item {
    width: 100%;
  }
}

.calendar-day {
  height: 100%;
  position: relative;
}

.log-item {
  margin-top: 2px;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #ecf5ff;
  color: #409EFF;
}

.log-item-content {
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 