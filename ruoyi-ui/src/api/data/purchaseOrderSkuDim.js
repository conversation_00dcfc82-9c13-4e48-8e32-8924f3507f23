import request from '@/utils/request'

// 查询采购单-sku维度列表
export function listPurchaseOrderSkuDim(query) {
  return request({
    url: '/data/purchaseOrderSkuDim/list',
    method: 'get',
    params: query
  })
}

// 查询采购单-sku维度详细
export function getPurchaseOrderSkuDim(id) {
  return request({
    url: '/data/purchaseOrderSkuDim/' + id,
    method: 'get'
  })
}

// 新增采购单-sku维度
export function addPurchaseOrderSkuDim(data) {
  return request({
    url: '/data/purchaseOrderSkuDim',
    method: 'post',
    data: data
  })
}

// 修改采购单-sku维度
export function updatePurchaseOrderSkuDim(data) {
  return request({
    url: '/data/purchaseOrderSkuDim',
    method: 'put',
    data: data
  })
}

// 删除采购单-sku维度
export function delPurchaseOrderSkuDim(id) {
  return request({
    url: '/data/purchaseOrderSkuDim/' + id,
    method: 'delete'
  })
}
