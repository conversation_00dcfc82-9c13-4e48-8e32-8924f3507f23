import request from '@/utils/request'

// 查询分销订单利润列表
export function listDistributionOrderProfit(query) {
  return request({
    url: '/data/distributionOrderProfit/list',
    method: 'get',
    params: query
  })
}

// 查询分销订单利润详细
export function getDistributionOrderProfit(id) {
  return request({
    url: '/data/distributionOrderProfit/' + id,
    method: 'get'
  })
}

// 新增分销订单利润
export function addDistributionOrderProfit(data) {
  return request({
    url: '/data/distributionOrderProfit',
    method: 'post',
    data: data
  })
}

// 修改分销订单利润
export function updateDistributionOrderProfit(data) {
  return request({
    url: '/data/distributionOrderProfit',
    method: 'put',
    data: data
  })
}

// 删除分销订单利润
export function delDistributionOrderProfit(id) {
  return request({
    url: '/data/distributionOrderProfit/' + id,
    method: 'delete'
  })
}

// 导出分销订单利润
export function exportDistributionOrderProfit(query) {
  return request({
    url: '/data/distributionOrderProfit/export',
    method: 'post',
    data: query
  })
}

// 流式导出分销订单利润（支持大数据量导出）
export function streamExportDistributionOrderProfit(query) {
  return request({
    url: '/data/distributionOrderProfit/streamExport',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 导入分销订单利润数据
export function importDistributionOrderProfit(data) {
  return request({
    url: '/data/distributionOrderProfit/importData',
    method: 'post',
    data: data
  })
}

// 下载分销订单利润导入模板
export function getDistributionOrderProfitImportTemplate() {
  return request({
    url: '/data/distributionOrderProfit/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
