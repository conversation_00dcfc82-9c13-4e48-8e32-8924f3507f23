package com.ruoyi.yimai.service.impl;

import java.util.List;
import java.io.IOException;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.io.FilenameUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileTypeUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.AliyunOSSUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.yimai.mapper.FileManagerMapper;
import com.ruoyi.yimai.domain.FileManager;
import com.ruoyi.yimai.service.IFileManagerService;

/**
 * 文件管理Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class FileManagerServiceImpl implements IFileManagerService 
{
    @Autowired
    private FileManagerMapper fileManagerMapper;
    
    @Autowired
    private AliyunOSSUtils aliyunOSSUtils;

    /**
     * 查询文件
     * 
     * @param fileId 文件ID
     * @return 文件
     */
    @Override
    public FileManager selectFileManagerById(Long fileId)
    {
        return fileManagerMapper.selectFileManagerById(fileId);
    }

    /**
     * 查询文件列表
     * 
     * @param fileManager 文件
     * @return 文件
     */
    @Override
    public List<FileManager> selectFileManagerList(FileManager fileManager)
    {
        return fileManagerMapper.selectFileManagerList(fileManager);
    }

    /**
     * 新增文件
     * 
     * @param fileManager 文件
     * @return 结果
     */
    @Override
    public int insertFileManager(FileManager fileManager)
    {
        fileManager.setCreateTime(DateUtils.getNowDate());
        return fileManagerMapper.insertFileManager(fileManager);
    }

    /**
     * 修改文件
     * 
     * @param fileManager 文件
     * @return 结果
     */
    @Override
    public int updateFileManager(FileManager fileManager)
    {
        fileManager.setUpdateTime(DateUtils.getNowDate());
        return fileManagerMapper.updateFileManager(fileManager);
    }

    /**
     * 批量删除文件
     * 
     * @param fileIds 需要删除的文件ID
     * @return 结果
     */
    @Override
    public int deleteFileManagerByIds(Long[] fileIds)
    {
        return fileManagerMapper.deleteFileManagerByIds(fileIds);
    }

    /**
     * 删除文件信息
     * 
     * @param fileId 文件ID
     * @return 结果
     */
    @Override
    public int deleteFileManagerById(Long fileId)
    {
        return fileManagerMapper.deleteFileManagerById(fileId);
    }
    
    /**
     * 上传文件
     * 
     * @param file 文件
     * @return 文件信息
     */
    @Override
    public FileManager uploadFile(MultipartFile file) throws Exception
    {
        // 上传文件到阿里云OSS
        String url = aliyunOSSUtils.uploadFile(file);
        
        // 获取文件信息
        String originalFilename = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(originalFilename);
        String fileType = getFileType(extension);
        
        // 创建文件信息
        FileManager fileManager = new FileManager();
        fileManager.setFileName(originalFilename);
        fileManager.setFilePath(url);
        fileManager.setFileType(fileType);
        fileManager.setFileSize(file.getSize());
        fileManager.setCreateBy(SecurityUtils.getUsername());
        fileManager.setCreateTime(DateUtils.getNowDate());
        
        // 保存文件信息
        fileManagerMapper.insertFileManager(fileManager);
        
        return fileManager;
    }
    
    /**
     * 更新文件关联信息
     * 
     * @param category 文件分类
     * @param remark 备注
     * @param fileIds 文件ID数组
     * @return 结果
     */
    @Override
    public int updateFileRelation(String category, String remark, Long[] fileIds)
    {
        int rows = 0;
        for (Long fileId : fileIds)
        {
            FileManager fileManager = fileManagerMapper.selectFileManagerById(fileId);
            if (fileManager != null)
            {
                fileManager.setCategory(category);
                fileManager.setRemark(remark);
                fileManager.setUpdateBy(SecurityUtils.getUsername());
                fileManager.setUpdateTime(DateUtils.getNowDate());
                rows += fileManagerMapper.updateFileManager(fileManager);
            }
        }
        return rows;
    }
    
    /**
     * 根据文件扩展名获取文件类型
     * 
     * @param extension 文件扩展名
     * @return 文件类型
     */
    private String getFileType(String extension)
    {
        if (extension == null)
        {
            return "other";
        }
        
        // 图片类型
        if (isImageExtension(extension))
        {
            return "image";
        }
        
        // 文档类型
        if (isDocumentExtension(extension))
        {
            return "document";
        }
        
        // 视频类型
        if (isVideoExtension(extension))
        {
            return "video";
        }
        
        // 音频类型
        if (isAudioExtension(extension))
        {
            return "audio";
        }
        
        // 其他类型
        return "other";
    }
    
    /**
     * 是否为图片扩展名
     */
    private boolean isImageExtension(String extension)
    {
        String[] imageExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        return isExtensionIn(extension, imageExtensions);
    }
    
    /**
     * 是否为文档扩展名
     */
    private boolean isDocumentExtension(String extension)
    {
        String[] docExtensions = {"doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "csv"};
        return isExtensionIn(extension, docExtensions);
    }
    
    /**
     * 是否为视频扩展名
     */
    private boolean isVideoExtension(String extension)
    {
        String[] videoExtensions = {"mp4", "avi", "mov", "wmv", "flv", "mkv"};
        return isExtensionIn(extension, videoExtensions);
    }
    
    /**
     * 是否为音频扩展名
     */
    private boolean isAudioExtension(String extension)
    {
        String[] audioExtensions = {"mp3", "wav", "wma", "ogg", "flac", "aac"};
        return isExtensionIn(extension, audioExtensions);
    }
    
    /**
     * 扩展名是否在指定数组中
     */
    private boolean isExtensionIn(String extension, String[] extensions)
    {
        if (extension == null)
        {
            return false;
        }
        
        extension = extension.toLowerCase();
        for (String ext : extensions)
        {
            if (ext.equals(extension))
            {
                return true;
            }
        }
        return false;
    }
} 