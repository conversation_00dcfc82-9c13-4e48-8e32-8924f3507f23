package com.ruoyi.yimai.service.impl;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.util.Date;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.yimai.domain.AmazonAccount;
import com.ruoyi.yimai.domain.ProductListingInfo;
import com.ruoyi.yimai.domain.RpaListingTask;
import com.ruoyi.yimai.domain.AiModels;
import com.ruoyi.yimai.domain.Prompt;
import com.ruoyi.yimai.domain.RpaListingJob;
import com.ruoyi.yimai.domain.FeisuanListingData;
import com.ruoyi.yimai.domain.K8sJobTemplate;
import com.ruoyi.yimai.mapper.ProductListingInfoMapper;
import com.ruoyi.yimai.service.IProductListingInfoService;
import com.ruoyi.yimai.service.KubernetesService;
import com.ruoyi.yimai.service.IAiModelsService;
import com.ruoyi.yimai.service.IPromptService;
import com.ruoyi.yimai.service.IAmazonAccountService;
import com.ruoyi.yimai.service.IRpaListingTaskService;
import com.ruoyi.yimai.service.IRpaListingJobService;
import com.ruoyi.yimai.service.IFeisuanListingDataService;
import com.ruoyi.yimai.service.K8sJobTemplateService;
import com.ruoyi.yimai.util.SpringContextUtil;

/**
 * 产品刊登信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
public class ProductListingInfoServiceImpl implements IProductListingInfoService 
{
    private static final Logger log = LoggerFactory.getLogger(ProductListingInfoServiceImpl.class);
    
    // 线程池配置参数，可通过配置文件修改
    @Value("${kubernetes.rpa.threadpool.core-size:20}")
    private int corePoolSize;
    
    @Value("${kubernetes.rpa.threadpool.max-size:50}")
    private int maxPoolSize;
    
    @Value("${kubernetes.rpa.threadpool.queue-capacity:100}")
    private int queueCapacity;
    
    @Value("${kubernetes.rpa.threadpool.keep-alive-seconds:300}")
    private int keepAliveSeconds;

    // RPA任务默认优先级类名
    @Value("${kubernetes.rpa.default-priority-class:low-priority}")
    private String defaultPriorityClass;

    // 创建一个更高级的线程池用于异步处理
    private ExecutorService executorService;
    
    @Autowired
    private ProductListingInfoMapper productListingInfoMapper;
    
    @Autowired
    private KubernetesService kubernetesService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private IAiModelsService aiModelsService;
    
    @Autowired
    private IPromptService promptService;
    
    @Autowired
    private IAmazonAccountService amazonAccountService;
    
    @Autowired
    private IRpaListingTaskService rpaListingTaskService;
    
    @Autowired
    private IRpaListingJobService rpaListingJobService;
    
    @Autowired
    private IFeisuanListingDataService feisuanListingDataService;
    
    @Autowired
    private K8sJobTemplateService k8sJobTemplateService;
    
    /**
     * 初始化方法，创建线程池
     */
    @javax.annotation.PostConstruct
    public void init() {
        // 创建线程池
        this.executorService = new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveSeconds,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        log.info("ProductListingInfoServiceImpl initialized with threadpool: coreSize={}, maxSize={}, queueCapacity={}, keepAliveSeconds={}", 
                corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds);
        log.info("RPA tasks will use priority class: {}", defaultPriorityClass);
    }
    
    /**
     * 销毁方法，关闭线程池
     */
    @javax.annotation.PreDestroy
    public void destroy() {
        log.info("Shutting down RPA thread pool");
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                // 等待所有任务完成
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    // 超时强制关闭
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 验证SKU是否有效
     * 排除以下情况：
     * 1. SKU以单独的大写字母开头，例如：D2056，D7841-25，I157-YM01
     * 2. SKU以su开头，例如su007
     * 
     * @param sku 要验证的SKU
     * @return 如果SKU有效返回true，否则返回false
     */
    private boolean isValidSku(String sku) {
        if (sku == null || sku.isEmpty()) {
            return false;
        }
        
        // 排除以单独大写字母开头的SKU
        if (sku.length() > 1 && Character.isUpperCase(sku.charAt(0)) && 
            (sku.charAt(1) == '-' || Character.isDigit(sku.charAt(1)))) {
            return false;
        }
        
        // 排除以su开头的SKU (不区分大小写)
        if (sku.toLowerCase().startsWith("su")) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public int startListing(ProductListingInfo productListingInfo)
    {
        // 1. 获取账号列表
        List<AmazonAccount> accountList = productListingInfo.getAccountList();
        
        // 2. 如果账号列表为空，直接返回0表示未处理
        if (accountList == null || accountList.isEmpty()) {
            return 0;
        }
        
        
        
        // 7. 为每个账号分配SKU并触发RPA
        int totalProcessed = 0;
        
        for (AmazonAccount account : accountList) {
            int postingNum = account.getPostingNum().intValue();
            if (postingNum <= 0) {
                continue;
            }
            
            int highScoreLimit = 0;
            int randomLimit = 0;
            if (postingNum > 0) {
                highScoreLimit = Math.max(1, (int) Math.floor(postingNum * 0.1));
                if (highScoreLimit > postingNum) {
                    highScoreLimit = postingNum;
                }
                randomLimit = postingNum - highScoreLimit;
                if (randomLimit < 0) {
                    randomLimit = 0;
                }
            }
            
            // 根据账号获取未刊登的SKU列表，包含attribute_category
            // 此方法已修改为10%/90%混合策略：10%选择高评分产品，90%随机选择产品
            List<Map<String, Object>> accountSkus = productListingInfoMapper.getUnlistedSkusWithAttributes(postingNum, account.getStore(), highScoreLimit, randomLimit);
            
            // 记录获取到的SKU数量，用于监控高评分产品是否充足
            if (accountSkus != null && !accountSkus.isEmpty()) {
                log.info("账号[{}]获取到{}个SKU，预期高评分产品{}个，随机产品{}个", 
                    account.getStore(), accountSkus.size(), highScoreLimit, randomLimit);
            }
            
            // 如果没有获取到SKU，跳过此账号
            if (accountSkus == null || accountSkus.isEmpty()) {
                continue;
            }
            
            // 过滤不符合条件的SKU
            accountSkus = accountSkus.stream()
                .filter(map -> isValidSku(String.valueOf(map.get("sku"))))
                .collect(java.util.stream.Collectors.toList());
            
            // 过滤后如果没有SKU，跳过此账号
            if (accountSkus.isEmpty()) {
                continue;
            }
            
            try {
                // 准备RPA任务数据
                RpaListingTask task = new RpaListingTask();
                task.setStore(account.getStore());
                task.setUserName(account.getUserName());
                task.setPassword(account.getPassword());
                
                // 提取SKU列表，用于任务对象存储
                List<String> skuList = accountSkus.stream()
                    .map(map -> String.valueOf(map.get("sku")))
                    .collect(java.util.stream.Collectors.toList());
                task.setSkuListObj(skuList);
                
                // 转换为所需的JSON格式: [{sku: "xxx", propertyType: "xxx"}]
                List<Map<String, String>> skuDataList = new java.util.ArrayList<>();
                for (Map<String, Object> skuInfo : accountSkus) {
                    Map<String, String> skuData = new java.util.HashMap<>();
                    skuData.put("sku", String.valueOf(skuInfo.get("sku")));
                    skuData.put("propertyType", String.valueOf(skuInfo.get("attribute_category")));
                    skuDataList.add(skuData);
                }
                
                // 转为JSON字符串存储
                String skuJsonData = objectMapper.writeValueAsString(skuDataList);
                task.setSkuList(skuJsonData);
                
                task.setAiModel(productListingInfo.getAiModel().getModelName());
                task.setPrompt(productListingInfo.getPrompt().getPrompt());
                task.setModelId(productListingInfo.getAiModel().getId());
                task.setPromptId(productListingInfo.getPrompt().getId());
                task.setStatus("PENDING");
                
                // 设置同步刊登数量，优先使用账号实体中的值，如果账号实体没有设置，则使用ProductListingInfo中的值
                Integer concurrentJobs = account.getConcurrentJobs();
                if (concurrentJobs == null || concurrentJobs < 1) {
                    // 如果账号中没有设置，则使用ProductListingInfo中的值
                    concurrentJobs = productListingInfo.getConcurrentJobs();
                    // 如果仍然为空，则默认为1
                    if (concurrentJobs == null || concurrentJobs < 1) {
                        concurrentJobs = 1;
                    }
                }
                task.setConcurrentJobs(concurrentJobs);
                
                // 保存任务到数据库
                rpaListingTaskService.insertRpaListingTask(task);
                
                // 获取数据库生成的自增ID
                Long taskId = task.getTaskId();
                
                // 将SKU插入到feisuan_listing_data表中
                for (Map<String, String> skuData : skuDataList) {
                    productListingInfoMapper.insertFeisuanListingData(
                        account.getUserName(),  // account
                        account.getStore(),     // store
                        skuData.get("sku"),     // sku
                        "",                     // site (空字符串)
                        new Date(),             // date
                        productListingInfo.getPrompt().getId(), // promptId
                        productListingInfo.getAiModel().getId(), // modelId
                        taskId                  // taskId
                    );
                }
                
                if (taskId != null && taskId > 0) {
                    log.info("成功创建RPA刊登任务，任务ID: {}, 店铺: {}, SKU数量: {}, 同步刊登数: {}", 
                           taskId, task.getStore(), skuDataList.size(), task.getConcurrentJobs());
                    
                    // 获取并检查同步刊登数量
                    int concurrentJobCount = task.getConcurrentJobs() != null ? task.getConcurrentJobs() : 1;
                    concurrentJobCount = Math.max(1, concurrentJobCount); // 至少为1
                    
                    // 根据同步刊登数量，将SKU平均分配给多个job
                    final List<Map<String, String>> allSkuList = new ArrayList<>(skuDataList);
                    final int totalSkus = allSkuList.size();
                    final int jobCount = Math.min(concurrentJobCount, totalSkus); // 若SKU数量小于并发数，则按SKU数量创建job
                    
                    // 为每个job异步创建Pod
                    for (int i = 0; i < jobCount; i++) {
                        final int jobIndex = i;
                        
                        // 计算当前job负责的SKU范围
                        int skusPerJob = totalSkus / jobCount;
                        int remainder = totalSkus % jobCount;
                        int startIndex = jobIndex * skusPerJob + Math.min(jobIndex, remainder);
                        int endIndex = startIndex + skusPerJob + (jobIndex < remainder ? 1 : 0);
                        
                        // 获取当前job的SKU子列表
                        final List<Map<String, String>> jobSkuList = allSkuList.subList(startIndex, endIndex);
                        
                        // 创建一个新的task副本，但共享同一个taskId
                        RpaListingTask jobTask = new RpaListingTask();
                        jobTask.setTaskId(taskId); // 使用相同的任务ID
                        jobTask.setStore(task.getStore());
                        jobTask.setUserName(task.getUserName());
                        jobTask.setPassword(task.getPassword());
                        jobTask.setAiModel(task.getAiModel());
                        jobTask.setPrompt(task.getPrompt());
                        jobTask.setModelId(task.getModelId());
                        jobTask.setPromptId(task.getPromptId());
                        jobTask.setStatus("PENDING");
                        
                        // 设置当前job的SKU列表
                        String jobSkuJsonData = objectMapper.writeValueAsString(jobSkuList);
                        jobTask.setSkuList(jobSkuJsonData);
                        
                        // 异步触发阿里云ACK创建Pod运行RPA程序
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 使用jobIndex作为Pod名称后缀，确保每个Pod名称唯一
                                String podSuffix = "-" + jobIndex;
                                boolean success = triggerRpaPod(taskId, jobTask, productListingInfo.getAiModel(), podSuffix);
                                
                                if (success) {
                                    log.info("任务 {} 子任务 {} Pod创建成功", taskId, jobIndex);
                                } else {
                                    log.error("任务 {} 子任务 {} Pod创建失败", taskId, jobIndex);
                                }
                            } catch (Exception e) {
                                log.error("Error in async pod creation for task " + taskId + ", job " + jobIndex, e);
                                // 更新任务状态为失败
                                updateTaskStatus(taskId, "FAILED", "Pod creation failed: " + e.getMessage());
                            }
                        }, executorService);
                    }
                    
                    // 立即计入处理数量，不等待Pod创建结果
                    totalProcessed += accountSkus.size();
                }
            } catch (Exception e) {
                log.error("Failed to process listing for store: " + account.getStore(), e);
            }
        }
        
        return totalProcessed;
    }
    
    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @param errorMessage 错误信息
     */
    private void updateTaskStatus(Long taskId, String status, String errorMessage) {
        try {
            RpaListingTask updateTask = new RpaListingTask();
            updateTask.setTaskId(taskId);
            updateTask.setStatus(status);
            if (errorMessage != null) {
                updateTask.setErrorMessage(errorMessage);
            }
            productListingInfoMapper.updateRpaTask(updateTask);
        } catch (Exception e) {
            log.error("Failed to update task status: " + taskId, e);
        }
    }
    
    /**
     * 触发阿里云ACK创建Pod运行RPA程序
     * 
     * @param taskId 任务ID
     * @param task RPA任务数据
     * @param aiModel AI模型信息
     * @param podSuffix Pod名称后缀，用于区分同一任务的多个Pod
     * @return 是否成功
     */
    private boolean triggerRpaPod(Long taskId, RpaListingTask task, AiModels aiModel, String podSuffix) {
        try {
            // 更新任务状态为运行中
            updateTaskStatus(taskId, "RUNNING", null);
            
            // 1. 准备Pod配置
            Map<String, Object> podConfig = new HashMap<>();
            podConfig.put("taskId", taskId);
            podConfig.put("store", task.getStore());
            podConfig.put("userName", task.getUserName());
            podConfig.put("password", task.getPassword());
            podConfig.put("SKU_DATA", task.getSkuList()); // 使用SKU_DATA作为环境变量键
            podConfig.put("aiModel", task.getAiModel());
            // 根据promptId获取完整的prompt内容
            String promptContent = null;
            if (task.getPromptId() != null) {
                Prompt prompt = promptService.selectPromptById(task.getPromptId());
                if (prompt != null) {
                    promptContent = prompt.getPrompt();
                }
            }
            podConfig.put("prompt", promptContent);
            // 添加AI模型的apiKey和url
            podConfig.put("aiModelApiKey", aiModel.getApiKey());
            podConfig.put("AIMODELSECRETKEY", aiModel.getApiSecret());
            // 指定Pod使用的Docker镜像
            podConfig.put("modelId", task.getModelId());
            podConfig.put("promptId", task.getPromptId());
            
            // 2. 生成Pod名称并添加jobName环境变量
            String podName = "rpa-" + taskId + (podSuffix != null ? podSuffix : "");
            podConfig.put("jobName", podName); // 添加jobName作为环境变量
            
            // 添加最长运行时间参数
            if (task.getMaxRunningSeconds() != null && task.getMaxRunningSeconds() > 0) {
                podConfig.put("activeDeadlineSeconds", task.getMaxRunningSeconds());
            }
            
            // 资源配置将从数据库模板配置中获取，不在这里硬编码
            // 如果需要覆盖默认配置，可以在podConfig中设置相应的值
            
            // 添加优先级类配置（使用PriorityClass）
            podConfig.put("priorityClassName", defaultPriorityClass);
            
            // 注释掉节点选择器配置，因为当前集群节点没有workload=batch标签
            // podConfig.put("nodeSelector", "workload=batch");
            
            // 解析出jobIndex
            Integer jobIndex = null;
            if (podSuffix != null && podSuffix.startsWith("-")) {
                try {
                    jobIndex = Integer.parseInt(podSuffix.substring(1));
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse job index from podSuffix: {}", podSuffix);
                }
            }
            
            // 创建RpaListingJob记录
            RpaListingJob job = new RpaListingJob();
            job.setTaskId(taskId);
            job.setJobName(podName);
            job.setJobIndex(jobIndex);
            job.setSkuList(task.getSkuList());
            job.setStatus("PENDING");
            job.setStartTime(new Date()); // 设置开始时间
            job.setProgress(0);
            
            try {
                // 保存RpaListingJob记录
                rpaListingJobService.insertRpaListingJob(job);
                log.info("成功创建RPA刊登子任务记录: {}, JobName: {}, JobIndex: {}", job.getJobId(), job.getJobName(), job.getJobIndex());
            } catch (Exception e) {
                log.error("创建RPA刊登子任务记录失败", e);
            }
            
            // 3. 调用创建Pod的方法
            log.info("调用kubernetesService.createJob, podName={}, templateName=rpa_listing, podConfig={}", podName, podConfig);
            return kubernetesService.createJob(podName, "rpa_listing", podConfig);
        } catch (Exception e) {
            log.error("Failed to trigger RPA pod for task: " + taskId, e);
            // 更新任务状态为失败
            updateTaskStatus(taskId, "FAILED", "Pod creation failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 重载原方法，兼容不传后缀的情况
     */
    private boolean triggerRpaPod(Long taskId, RpaListingTask task, AiModels aiModel) {
        return triggerRpaPod(taskId, task, aiModel, null);
    }

    /**
     * 从导入的Excel创建刊登任务
     */
    @Override
    public int createListingTaskFromImport(List<Map<String, String>> productList, Long modelId, Long promptId, List<Long> accountIds, Integer maxRunningSeconds) {
        // 检查参数有效性
        if (productList == null || productList.isEmpty() || accountIds == null || accountIds.isEmpty()) {
            return 0;
        }
        
        // 获取AI模型信息
        AiModels aiModel = aiModelsService.selectAiModelsById(modelId);
        if (aiModel == null) {
            throw new RuntimeException("未找到指定的AI模型");
        }
        
        // 获取提示词信息
        Prompt prompt = promptService.selectPromptById(promptId);
        if (prompt == null) {
            throw new RuntimeException("未找到指定的提示词");
        }
        
        // 验证最长运行时间
        if (maxRunningSeconds == null || maxRunningSeconds < 600) {
            maxRunningSeconds = 3600; // 默认为1小时
        } else if (maxRunningSeconds > 86400) {
            maxRunningSeconds = 86400; // 限制最大值为24小时
        }
        
        // 使用Set去重Excel中的SKU
        Set<String> uniqueSkus = new HashSet<>();
        List<Map<String, String>> uniqueProductList = new ArrayList<>();
        
        for (Map<String, String> product : productList) {
            String sku = product.get("sku");
            if (sku != null && !sku.isEmpty() && isValidSku(sku) && uniqueSkus.add(sku)) {
                uniqueProductList.add(product);
            } else if (sku != null && !sku.isEmpty() && !isValidSku(sku)) {
                log.info("SKU {} 不符合规则，将被排除", sku);
            }
        }
        
        if (uniqueProductList.isEmpty()) {
            log.warn("导入的产品列表中没有有效的SKU，无法创建任务");
            return 0;
        }
        
        log.info("导入的产品列表中有{}个有效SKU", uniqueProductList.size());
        
        int totalProcessed = 0;
        
        // 为每个选中的账号创建任务
        for (Long accountId : accountIds) {
            try {
                // 获取亚马逊账号信息
                AmazonAccount account = amazonAccountService.selectAmazonAccountById(accountId);
                if (account == null) {
                    log.warn("未找到ID为{}的亚马逊账号，跳过处理", accountId);
                    continue;
                }
                
                // 从feisuan_listing_data表中查询已存在的SKU
                List<String> existingSkus = productListingInfoMapper.selectExistingSkus(uniqueSkus, account.getStore());
                if (existingSkus != null && !existingSkus.isEmpty()) {
                    // 从uniqueProductList中移除已存在的SKU
                    uniqueProductList.removeIf(product -> existingSkus.contains(product.get("sku")));
                    log.info("发现{}个SKU已在店铺{}中存在，已自动过滤", existingSkus.size(), account.getStore());
                }
                
                if (uniqueProductList.isEmpty()) {
                    log.warn("所有SKU都已存在于店铺{}中，无需创建新任务", account.getStore());
                    continue;
                }
                
                // 获取账号的同步刊登数量设置
                Integer concurrentJobs = account.getConcurrentJobs();
                if (concurrentJobs == null || concurrentJobs < 1) {
                    concurrentJobs = 1; // 默认为1
                } else if (concurrentJobs > 10) {
                    concurrentJobs = 10; // 限制最大值为10
                }
                
                // 准备RPA任务数据
                RpaListingTask task = new RpaListingTask();
                task.setStore(account.getStore());
                task.setUserName(account.getUserName());
                task.setPassword(account.getPassword());
                
                // 提取SKU列表，用于任务对象存储
                List<String> skuList = new ArrayList<>();
                for (Map<String, String> product : uniqueProductList) {
                    skuList.add(product.get("sku"));
                }
                task.setSkuListObj(skuList);
                
                // 转为JSON字符串存储
                String skuJsonData = objectMapper.writeValueAsString(uniqueProductList);
                task.setSkuList(skuJsonData);
                
                task.setAiModel(aiModel.getModelName());
                task.setPrompt(prompt.getPrompt());
                task.setModelId(aiModel.getId());
                task.setPromptId(prompt.getId());
                task.setStatus("PENDING");
                
                // 设置同步刊登数量
                task.setConcurrentJobs(concurrentJobs);
                
                // 设置最长运行时间
                task.setMaxRunningSeconds(maxRunningSeconds);
                
                // 保存任务到数据库
                rpaListingTaskService.insertRpaListingTask(task);
                
                // 获取数据库生成的自增ID
                Long taskId = task.getTaskId();
                
                // 将SKU插入到feisuan_listing_data表中
                for (Map<String, String> skuData : uniqueProductList) {
                    productListingInfoMapper.insertFeisuanListingData(
                        account.getUserName(),  // account
                        account.getStore(),     // store
                        skuData.get("sku"),     // sku
                        "",                     // site (空字符串)
                        new Date(),             // date
                        prompt.getId(),          // promptId
                        aiModel.getId(),         // modelId
                        taskId                  // taskId
                    );
                }
                
                if (taskId != null && taskId > 0) {
                    log.info("成功创建RPA刊登任务，任务ID: {}, 店铺: {}, SKU数量: {}, 同步刊登数: {}, 最长运行时间: {}秒", 
                           taskId, task.getStore(), uniqueProductList.size(), task.getConcurrentJobs(), task.getMaxRunningSeconds());
                    
                    // 获取并检查同步刊登数量
                    int concurrentJobCount = task.getConcurrentJobs();
                    concurrentJobCount = Math.max(1, concurrentJobCount); // 至少为1
                    
                    // 根据同步刊登数量，将SKU平均分配给多个job
                    final List<Map<String, String>> allSkuList = new ArrayList<>(uniqueProductList);
                    final int totalSkus = allSkuList.size();
                    final int jobCount = Math.min(concurrentJobCount, totalSkus); // 若SKU数量小于并发数，则按SKU数量创建job
                    
                    // 为每个job异步创建Pod
                    for (int i = 0; i < jobCount; i++) {
                        final int jobIndex = i;
                        
                        // 计算当前job负责的SKU范围
                        int skusPerJob = totalSkus / jobCount;
                        int remainder = totalSkus % jobCount;
                        int startIndex = jobIndex * skusPerJob + Math.min(jobIndex, remainder);
                        int endIndex = startIndex + skusPerJob + (jobIndex < remainder ? 1 : 0);
                        
                        // 获取当前job的SKU子列表
                        final List<Map<String, String>> jobSkuList = allSkuList.subList(startIndex, endIndex);
                        
                        // 创建一个新的task副本，但共享同一个taskId
                        RpaListingTask jobTask = new RpaListingTask();
                        jobTask.setTaskId(taskId); // 使用相同的任务ID
                        jobTask.setStore(task.getStore());
                        jobTask.setUserName(task.getUserName());
                        jobTask.setPassword(task.getPassword());
                        jobTask.setAiModel(task.getAiModel());
                        jobTask.setPrompt(task.getPrompt());
                        jobTask.setModelId(task.getModelId());
                        jobTask.setPromptId(task.getPromptId());
                        jobTask.setStatus("PENDING");
                        
                        // 设置当前job的SKU列表
                        String jobSkuJsonData = objectMapper.writeValueAsString(jobSkuList);
                        jobTask.setSkuList(jobSkuJsonData);
                        
                        // 异步触发阿里云ACK创建Pod运行RPA程序
                        CompletableFuture.runAsync(() -> {
                            try {
                                // 使用jobIndex作为Pod名称后缀，确保每个Pod名称唯一
                                String podSuffix = "-" + jobIndex;
                                boolean success = triggerRpaPod(taskId, jobTask, aiModel, podSuffix);
                                
                                if (success) {
                                    log.info("任务 {} 子任务 {} Pod创建成功", taskId, jobIndex);
                                } else {
                                    log.error("任务 {} 子任务 {} Pod创建失败", taskId, jobIndex);
                                }
                            } catch (Exception e) {
                                log.error("Error in async pod creation for task " + taskId + ", job " + jobIndex, e);
                                // 更新任务状态为失败
                                updateTaskStatus(taskId, "FAILED", "Pod creation failed: " + e.getMessage());
                            }
                        }, executorService);
                    }
                    
                    // 累加处理的产品数量
                    totalProcessed += uniqueProductList.size();
                }
            } catch (Exception e) {
                log.error("处理账号ID: " + accountId + "的导入产品失败", e);
                // 继续处理下一个账号，不中断整个流程
            }
        }
        
        return totalProcessed;
    }

    /**
     * 处理失败的任务，重新创建任务继续处理
     */
    public void handleFailedTasks() {
        // 获取所有失败的子任务
        List<RpaListingJob> failedJobs = rpaListingJobService.selectFailedJobs();
        
        for (RpaListingJob failedJob : failedJobs) {
            try {
                Long taskId = failedJob.getTaskId();
                String jobName = failedJob.getJobName();
                String currentSku = failedJob.getCurrentSku();
                
                // 如果没有记录当前处理的SKU，无法继续处理
                if (currentSku == null || currentSku.isEmpty()) {
                    log.warn("任务 {} 的子任务 {} 没有记录当前处理的SKU，无法继续处理", taskId, jobName);
                    continue;
                }
                
                // 获取任务信息
                RpaListingTask task = rpaListingTaskService.selectRpaListingTaskByTaskId(taskId);
                if (task == null) {
                    log.warn("未找到任务 {}", taskId);
                    continue;
                }
                
                // 解析子任务的SKU列表
                List<Map<String, String>> jobSkuList = objectMapper.readValue(failedJob.getSkuList(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class));
                
                // 找到currentSku在列表中的位置
                int skuIndex = -1;
                for (int i = 0; i < jobSkuList.size(); i++) {
                    if (jobSkuList.get(i).get("sku").equals(currentSku)) {
                        skuIndex = i;
                        break;
                    }
                }
                
                if (skuIndex == -1) {
                    log.warn("在子任务 {} 的SKU列表中未找到当前处理的SKU: {}", jobName, currentSku);
                    continue;
                }
                
                // 获取剩余的SKU
                List<Map<String, String>> remainingSkus = jobSkuList.subList(skuIndex + 1, jobSkuList.size());
                
                if (remainingSkus.isEmpty()) {
                    log.info("子任务 {} 没有剩余的SKU需要处理", jobName);
                    // 更新子任务状态为已完成
                    RpaListingJob updateJob = new RpaListingJob();
                    updateJob.setJobId(failedJob.getJobId());
                    updateJob.setStatus("COMPLETED");
                    updateJob.setEndTime(new Date());
                    updateJob.setProgress(100);
                    rpaListingJobService.updateRpaListingJob(updateJob);
                    continue;
                }
                
                // 获取AI模型信息
                AiModels aiModel = aiModelsService.selectAiModelsById(task.getModelId());
                if (aiModel == null) {
                    log.error("未找到任务 {} 使用的AI模型 {}", taskId, task.getModelId());
                    continue;
                }
                
                // 将剩余SKU列表转换为JSON字符串
                String remainingSkuJsonData = objectMapper.writeValueAsString(remainingSkus);
                
                // 准备Pod配置
                Map<String, Object> podConfig = new HashMap<>();
                podConfig.put("taskId", taskId);
                podConfig.put("store", task.getStore());
                podConfig.put("userName", task.getUserName());
                podConfig.put("password", task.getPassword());
                podConfig.put("SKU_DATA", remainingSkuJsonData);
                podConfig.put("aiModel", task.getAiModel());
                // 根据promptId获取完整的prompt内容
                String promptContent = null;
                if (task.getPromptId() != null) {
                    Prompt prompt = promptService.selectPromptById(task.getPromptId());
                    if (prompt != null) {
                        promptContent = prompt.getPrompt();
                    }
                }
                podConfig.put("prompt", promptContent);
                podConfig.put("aiModelApiKey", aiModel.getApiKey());
                podConfig.put("AIMODELSECRETKEY", aiModel.getApiSecret());
                podConfig.put("modelId", task.getModelId());
                podConfig.put("promptId", task.getPromptId());
                podConfig.put("jobName", jobName);
                
                if (task.getMaxRunningSeconds() != null && task.getMaxRunningSeconds() > 0) {
                    podConfig.put("activeDeadlineSeconds", task.getMaxRunningSeconds());
                }
                
                // 调用创建Pod的方法
                boolean success = kubernetesService.createJob(jobName, "rpa_listing", podConfig);
                
                if (success) {
                    log.info("成功重新启动子任务 {}，处理 {} 个剩余的SKU", jobName, remainingSkus.size());
                    
                    // 更新子任务状态
                    RpaListingJob updateJob = new RpaListingJob();
                    updateJob.setJobId(failedJob.getJobId());
                    updateJob.setStatus("RUNNING");
                    updateJob.setErrorMessage(null); // 清空错误信息
                    updateJob.setSkuList(remainingSkuJsonData); // 更新SKU列表为剩余的SKU
                    rpaListingJobService.updateRpaListingJob(updateJob);
                    
                    // 更新任务状态
                    RpaListingTask updateTask = new RpaListingTask();
                    updateTask.setTaskId(taskId);
                    updateTask.setStatus("RUNNING");
                    rpaListingTaskService.updateRpaListingTask(updateTask);
                } else {
                    log.error("重新启动子任务 {} 失败", jobName);
                }
            } catch (Exception e) {
                log.error("处理失败的子任务 {} 时出错", failedJob.getJobId(), e);
            }
        }
    }

    @Override
    public void autoListing() {
        log.info("开始执行自动刊登任务");
        
        try {
            // 获取设置了自动刊登的账号列表
            List<AmazonAccount> autoListingAccounts = amazonAccountService.selectAutoListingAccounts();
            if (autoListingAccounts.isEmpty()) {
                log.info("没有设置自动刊登的账号，跳过处理");
                return;
            }
            
            log.info("找到{}个自动刊登账号", autoListingAccounts.size());
            
            // 处理每个账号
            for (AmazonAccount account : autoListingAccounts) {
                String store = account.getStore();
                
                // 检查当前店铺是否有运行中的job
                List<RpaListingJob> runningJobs = rpaListingJobService.selectRunningJobsByStore(store);
                if (!runningJobs.isEmpty()) {
                    log.info("账号[{}]当前有{}个运行中的任务，跳过处理", account.getUserName(), runningJobs.size());
                    continue;
                }
                
                // 获取该店铺当天已成功刊登的数据
                List<FeisuanListingData> successListingData = feisuanListingDataService.getSuccessListingDataByStoreToday(store);
                
                log.info("账号[{}]今日已成功刊登{}条数据", account.getUserName(), successListingData.size());
                
                // 计算还需要刊登的数量（postingNum - 已成功刊登数量）
                Long postingNum = account.getPostingNum();
                if (postingNum == null || postingNum <= 0) {
                    log.info("账号[{}]的刊登数量设置为0，跳过处理", account.getUserName());
                    continue;
                }
                
                int successCount = successListingData.size();
                int remainingCount = postingNum.intValue() - successCount;
                
                // 如果已经达到或超过了刊登数量，则跳过此账号
                if (remainingCount <= 0) {
                    log.info("账号[{}]今日已达到刊登数量目标({})，跳过处理", account.getUserName(), postingNum);
                    continue;
                }
                
                // 刊登数量按阈值处理
                if (remainingCount < 10) {
                    remainingCount = 10;
                    log.info("账号[{}]剩余刊登数量小于10，按10处理", account.getUserName());
                } else if (remainingCount < 25) {
                    remainingCount = 25;
                    log.info("账号[{}]剩余刊登数量小于25，按25处理", account.getUserName());
                } else if (remainingCount < 50) {
                    remainingCount = 50;
                    log.info("账号[{}]剩余刊登数量小于50，按50处理", account.getUserName());
                }
                
                log.info("账号[{}]还需要刊登{}个产品", account.getUserName(), remainingCount);
                
                // 构建一个ProductListingInfo对象，用于调用startListing方法
                ProductListingInfo productListingInfo = new ProductListingInfo();
                
                // 设置账号列表，并临时修改postingNum为剩余需要刊登的数量（不修改数据库的值）
                List<AmazonAccount> accountList = new ArrayList<>();
                // 创建一个新的账号对象，避免修改原有对象
                AmazonAccount accountCopy = new AmazonAccount();
                accountCopy.setId(account.getId());
                accountCopy.setUserName(account.getUserName());
                accountCopy.setPassword(account.getPassword());
                accountCopy.setStore(account.getStore());
                accountCopy.setPostingNum((long) remainingCount); // 设置为剩余需要刊登的数量
                accountCopy.setConcurrentJobs(account.getConcurrentJobs());
                accountCopy.setDefaultModelId(account.getDefaultModelId());
                accountCopy.setDefaultPromptId(account.getDefaultPromptId());
                accountList.add(accountCopy);
                productListingInfo.setAccountList(accountList);
                
                // 设置AI模型和提示词
                AiModels aiModel = aiModelsService.selectAiModelsById(account.getDefaultModelId());
                Prompt prompt = promptService.selectPromptById(account.getDefaultPromptId());
                
                if (aiModel == null || prompt == null) {
                    log.error("账号[{}]缺少默认AI模型或提示词设置，跳过处理", account.getUserName());
                    continue;
                }
                
                productListingInfo.setAiModel(aiModel);
                productListingInfo.setPrompt(prompt);
                
                // 设置同步刊登数量
                if (account.getConcurrentJobs() != null && account.getConcurrentJobs() > 0) {
                    productListingInfo.setConcurrentJobs(account.getConcurrentJobs());
                } else {
                    productListingInfo.setConcurrentJobs(1); // 默认为1
                }
                
                // 调用startListing方法创建刊登任务
                startListing(productListingInfo);
                log.info("账号[{}]自动刊登任务创建成功", account.getUserName());
            }
            
            log.info("自动刊登任务执行完成");
        } catch (Exception e) {
            log.error("执行自动刊登任务时发生错误", e);
        }
    }

    /**
     * 检查并重启未完成但在阿里云集群中不存在的任务
     */
    @Override
    public void checkAndRestartRunningJobs() {
        log.info("开始检查未完成的RPA刊登任务");
        
        try {
            // 查询所有非COMPLETED状态的RpaListingJob
            List<RpaListingJob> runningJobs = rpaListingJobService.selectNonCompletedJobs();
            
            if (runningJobs.isEmpty()) {
                log.info("没有未完成的RPA刊登任务，跳过处理");
                return;
            }
            
            log.info("找到{}个未完成的RPA刊登任务", runningJobs.size());
            
            // 遍历每个未完成的任务
            for (RpaListingJob job : runningJobs) {
                try {
                    String jobName = job.getJobName();
                    
                    // 获取模板配置
                    K8sJobTemplate template = k8sJobTemplateService.getTemplateByName("rpa_listing");
                    if (template == null) {
                        log.error("模板不存在: rpa_listing");
                        continue;
                    }
                    
                    // 获取命名空间
                    String namespace = template.getNamespace();
                    if (namespace == null || namespace.isEmpty()) {
                        log.error("模板 rpa_listing 中未配置namespace");
                        continue;
                    }
                    
                    // 检查任务在阿里云集群中是否存在
                    boolean jobExists = kubernetesService.checkJobExists(jobName, namespace);
                    
                    if (jobExists) {
                        log.info("任务{}在阿里云集群中存在，不需要重启", jobName);
                        continue;
                    }
                    
                    log.info("任务{}在阿里云集群中不存在，需要重启", jobName);
                    
                    // 获取任务的详细信息
                    Long taskId = job.getTaskId();
                    RpaListingTask task = rpaListingTaskService.selectRpaListingTaskByTaskId(taskId);
                    
                    if (task == null) {
                        log.error("未找到任务ID为{}的任务信息，无法重启任务{}", taskId, jobName);
                        continue;
                    }
                    
                    // 获取AI模型信息
                    AiModels aiModel = aiModelsService.selectAiModelsById(task.getModelId());
                    if (aiModel == null) {
                        log.error("未找到任务{}使用的AI模型ID:{}", taskId, task.getModelId());
                        continue;
                    }
                    
                    // 准备Pod配置
                    Map<String, Object> podConfig = new HashMap<>();
                    podConfig.put("taskId", taskId);
                    podConfig.put("store", task.getStore());
                    podConfig.put("userName", task.getUserName());
                    podConfig.put("password", task.getPassword());
                    podConfig.put("SKU_DATA", job.getSkuList()); // 直接使用RpaListingJob中的SKU列表
                    podConfig.put("aiModel", task.getAiModel());
                    
                    // 获取提示词内容
                    String promptContent = null;
                    if (task.getPromptId() != null) {
                        Prompt prompt = promptService.selectPromptById(task.getPromptId());
                        if (prompt != null) {
                            promptContent = prompt.getPrompt();
                        }
                    }
                    podConfig.put("prompt", promptContent);
                    
                    // 添加AI模型信息
                    podConfig.put("aiModelApiKey", aiModel.getApiKey());
                    podConfig.put("AIMODELSECRETKEY", aiModel.getApiSecret());
                    podConfig.put("modelId", task.getModelId());
                    podConfig.put("promptId", task.getPromptId());
                    podConfig.put("jobName", jobName);
                    
                    // 设置最长运行时间
                    if (task.getMaxRunningSeconds() != null && task.getMaxRunningSeconds() > 0) {
                        podConfig.put("activeDeadlineSeconds", task.getMaxRunningSeconds());
                    }
                    
                    // 资源配置将从数据库模板配置中获取，不在这里硬编码
                    // 如果需要覆盖默认配置，可以在podConfig中设置相应的值
                    
                    // 添加优先级类配置（使用PriorityClass）
                    podConfig.put("priorityClassName", defaultPriorityClass);
                    
                    // 注释掉节点选择器配置，因为当前集群节点没有workload=batch标签
                    // podConfig.put("nodeSelector", "workload=batch");
                    
                    // 创建Pod
                    boolean success = kubernetesService.createJob(jobName, "rpa_listing", podConfig);
                    
                    if (success) {
                        log.info("成功重启任务{}", jobName);
                        
                        // 更新任务状态
                        RpaListingJob updateJob = new RpaListingJob();
                        updateJob.setJobId(job.getJobId());
                        updateJob.setStatus("RUNNING");
                    
                        rpaListingJobService.updateRpaListingJob(updateJob);
                        
                        // 更新父任务状态
                        RpaListingTask updateTask = new RpaListingTask();
                        updateTask.setTaskId(taskId);
                        updateTask.setStatus("RUNNING");
                        rpaListingTaskService.updateRpaListingTask(updateTask);
                    } else {
                        log.error("重启任务{}失败", jobName);
                    }
                } catch (Exception e) {
                    log.error("处理任务{}时发生错误", job.getJobName(), e);
                }
            }
            
            log.info("检查未完成的RPA刊登任务完成");
        } catch (Exception e) {
            log.error("检查未完成的RPA刊登任务时发生错误", e);
        }
    }


} 