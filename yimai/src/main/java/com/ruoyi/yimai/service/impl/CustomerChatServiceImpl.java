package com.ruoyi.yimai.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.yimai.domain.CustomerChatSession;
import com.ruoyi.yimai.domain.CustomerChatMessage;
import com.ruoyi.yimai.domain.Customer;
import com.ruoyi.yimai.domain.Shop;
import com.ruoyi.yimai.service.ICustomerChatService;
import com.ruoyi.yimai.service.ICustomerService;
import com.ruoyi.yimai.service.IShopService;
import com.ruoyi.yimai.service.IFeisuanListingDataService;
import com.ruoyi.yimai.aimodel.utils.DSR1ModelUtil;
import com.ruoyi.yimai.aimodel.utils.DSV3ModelUtil;

/**
 * 客户聊天Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class CustomerChatServiceImpl implements ICustomerChatService 
{
    @Autowired
    private ICustomerService customerService;
    
    @Autowired
    private IShopService shopService;
    
    @Autowired
    private IFeisuanListingDataService feisuanListingDataService;

    // 预定义意图选项
    private static final List<String> INTENT_OPTIONS = Arrays.asList(
        "CUSTOMER_INFO",      // 客户基本信息
        "SHOP_INFO",          // 店铺信息
        "SHOP_STATISTICS",    // 店铺统计
        "LISTING_STATISTICS", // 刊登统计
        "ORDER_INFO",         // 订单信息
        "PRODUCT_INFO",       // 产品信息
        "ACCOUNT_INFO",       // 账户信息
        "GENERAL_HELP"        // 一般帮助
    );

    @Override
    public String processCustomerMessage(Long customerId, String message) {
        try {
            // 1. 分析用户意图
            Map<String, Object> intentResult = analyzeIntent(message);
            @SuppressWarnings("unchecked")
            List<String> intentTypes = (List<String>) intentResult.get("intentTypes");
            
            // 2. 根据意图查询相关数据
            Map<String, Object> queryResults = queryDataByIntents(customerId, intentTypes, message);
            
            // 3. 构建AI提示词
            String prompt = buildAIPrompt(customerId, message, intentTypes, queryResults);
            
            // 4. 调用AI模型 - 使用DeepSeek-V3
            String aiResponse = DSV3ModelUtil.chat(prompt);
            
            return aiResponse;
            
        } catch (Exception e) {
            return "抱歉，我遇到了一些问题，请稍后再试。错误信息：" + e.getMessage();
        }
    }

    @Override
    public CustomerChatSession getOrCreateSession(Long customerId) {
        // 简化实现：直接返回模拟会话
        CustomerChatSession session = new CustomerChatSession();
        session.setSessionId(System.currentTimeMillis());
        session.setCustomerId(customerId);
        session.setSessionStatus("ACTIVE");
        session.setLastActiveTime(new Date());
        session.setTurnCount(1);
        return session;
    }

    @Override
    public Long saveMessage(Long sessionId, String content, String senderType, String intentType) {
        // 简化实现：直接返回时间戳作为消息ID
        return System.currentTimeMillis();
    }

    @Override
    public Customer getCustomerInfo(Long customerId) {
        return customerService.selectCustomerByCustomerId(customerId);
    }

    @Override
    public List<Shop> getCustomerShops(Long customerId) {
        return shopService.selectShopListByCustomerId(customerId);
    }

    @Override
    public Shop getShopByName(Long customerId, String shopName) {
        List<Shop> shops = getCustomerShops(customerId);
        return shops.stream()
                .filter(shop -> shop.getShopFullName().contains(shopName) || 
                               shop.getShopShortName().contains(shopName) ||
                               shop.getBrandName().contains(shopName))
                .findFirst()
                .orElse(null);
    }

    @Override
    public Map<String, Object> getCustomerStatistics(Long customerId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取客户信息
        Customer customer = getCustomerInfo(customerId);
        List<Shop> shops = getCustomerShops(customerId);
        
        long activeCount = shops.stream()
                .filter(shop -> "1".equals(shop.getActiveStatus()))
                .count();
        
        statistics.put("customerName", customer.getRealName());
        statistics.put("shopCount", shops.size());
        statistics.put("activeShopCount", activeCount);
        statistics.put("inactiveShopCount", shops.size() - activeCount);
        statistics.put("activeStatusText", activeCount + "个活跃店铺，" + (shops.size() - activeCount) + "个非活跃店铺");
        statistics.put("customerType", customer.getCustomerType());
        statistics.put("customerStatus", customer.getCustomerStatus());
        
        return statistics;
    }
    
    /**
     * 获取客户刊登数量统计
     * 
     * @param customerId 客户ID
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 刊登数量统计
     */
    public Map<String, Object> getCustomerListingStatistics(Long customerId, String startDate, String endDate) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 获取客户信息
            Customer customer = getCustomerInfo(customerId);
            List<Shop> shops = getCustomerShops(customerId);
            
            // 提取所有店铺的shop_full_name作为store
            List<String> stores = shops.stream()
                    .map(Shop::getShopFullName)
                    .filter(store -> store != null && !store.isEmpty())
                    .collect(Collectors.toList());
            
            if (stores.isEmpty()) {
                statistics.put("customerName", customer.getRealName());
                statistics.put("message", "该客户暂无店铺信息");
                statistics.put("totalListings", 0);
                statistics.put("stores", new ArrayList<>());
                return statistics;
            }
            
            // 统计每个店铺的刊登数量
            List<Map<String, Object>> storeStats = new ArrayList<>();
            int totalListings = 0;
            
            for (String store : stores) {
                // 查询该店铺在指定时间区间的刊登数量
                int listingCount = feisuanListingDataService.countListingDataByStore(store, startDate, endDate);
                
                Map<String, Object> storeStat = new HashMap<>();
                storeStat.put("store", store);
                storeStat.put("listingCount", listingCount);
                storeStats.add(storeStat);
                
                totalListings += listingCount;
            }
            
            statistics.put("customerName", customer.getRealName());
            statistics.put("startDate", startDate);
            statistics.put("endDate", endDate);
            statistics.put("totalListings", totalListings);
            statistics.put("storeCount", stores.size());
            statistics.put("stores", storeStats);
            statistics.put("summary", String.format("客户%s在%s至%s期间，共%d个店铺，总刊登数量为%d", 
                    customer.getRealName(), startDate, endDate, stores.size(), totalListings));
            
        } catch (Exception e) {
            statistics.put("error", "查询刊登统计时发生错误: " + e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public List<CustomerChatMessage> getChatHistory(Long sessionId, Integer limit) {
        // 这里应该查询数据库，暂时返回空列表
        return new java.util.ArrayList<>();
    }

    @Override
    public Map<String, Object> analyzeIntent(String message) {
        // 构建意图分析提示词
        String intentPrompt = buildIntentAnalysisPrompt(message);
        
        // 调用AI分析意图 - 使用DeepSeek-V3
        String aiIntentResponse = DSV3ModelUtil.chat(intentPrompt);
        
        // 解析AI返回的意图
        List<String> detectedIntents = parseIntentResponse(aiIntentResponse);
        
        Map<String, Object> result = new HashMap<>();
        result.put("intentTypes", detectedIntents);
        result.put("confidence", 0.9);
        return result;
    }

    @Override
    public String buildContextInfo(Long customerId, Long sessionId) {
        try {
            // 获取客户基本信息
            Customer customer = getCustomerInfo(customerId);
            List<Shop> shops = getCustomerShops(customerId);
            
            JSONObject context = new JSONObject();
            context.put("customerId", customerId);
            context.put("customerName", customer.getRealName());
            context.put("customerType", customer.getCustomerType());
            context.put("customerStatus", customer.getCustomerStatus());
            context.put("customerPhone", customer.getCustomerPhone());
            context.put("customerEmail", customer.getCustomerEmail());
            context.put("customerAddress", customer.getCustomerAddress());
            
            // 店铺信息
            JSONObject shopsInfo = new JSONObject();
            shopsInfo.put("totalCount", shops.size());
            shopsInfo.put("shops", shops.stream().map(shop -> {
                JSONObject shopInfo = new JSONObject();
                shopInfo.put("shopId", shop.getShopId());
                shopInfo.put("shopFullName", shop.getShopFullName());
                shopInfo.put("shopShortName", shop.getShopShortName());
                shopInfo.put("brandName", shop.getBrandName());
                shopInfo.put("companyName", shop.getCompanyName());
                shopInfo.put("activeStatus", shop.getActiveStatus());
                shopInfo.put("legalPerson", shop.getLegalPerson());
                shopInfo.put("contractTime", shop.getContractTime());
                return shopInfo;
            }).collect(Collectors.toList()));
            
            context.put("shops", shopsInfo);
            
            return context.toJSONString();
            
        } catch (Exception e) {
            return "{}";
        }
    }

    @Override
    public void updateSessionStatus(Long sessionId, String status) {
        // 这里应该更新数据库
    }



    /**
     * 构建意图分析提示词
     */
    private String buildIntentAnalysisPrompt(String message) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个意图分析专家。请分析以下用户问题的意图，并从预定义的意图选项中选择最匹配的一个或多个。\n\n");
        prompt.append("预定义意图选项：\n");
        for (String intent : INTENT_OPTIONS) {
            prompt.append("- ").append(intent).append("\n");
        }
        prompt.append("\n意图说明：\n");
        prompt.append("- CUSTOMER_INFO: 查询客户基本信息（姓名、电话、邮箱、地址等）\n");
        prompt.append("- SHOP_INFO: 查询店铺详细信息（店铺名称、品牌、公司、法人等）\n");
        prompt.append("- SHOP_STATISTICS: 查询店铺统计数据（店铺数量、活跃状态等）\n");
        prompt.append("- LISTING_STATISTICS: 查询刊登统计数据（各店铺的刊登数量、时间区间等）\n");
        prompt.append("- ORDER_INFO: 查询订单相关信息\n");
        prompt.append("- PRODUCT_INFO: 查询产品相关信息\n");
        prompt.append("- ACCOUNT_INFO: 查询账户相关信息\n");
        prompt.append("- GENERAL_HELP: 一般帮助和说明\n\n");
        prompt.append("重要区分：\n");
        prompt.append("- SHOP_STATISTICS: 关注店铺本身的数量和状态统计\n");
        prompt.append("- LISTING_STATISTICS: 关注产品刊登的数量统计，通常包含时间区间\n");
        prompt.append("- 如果用户询问\"刊登数量\"、\"刊登统计\"、\"上个月的刊登\"等，应该选择LISTING_STATISTICS\n");
        prompt.append("- 如果用户询问\"店铺数量\"、\"活跃店铺\"等，应该选择SHOP_STATISTICS\n\n");
        prompt.append("用户问题：").append(message).append("\n\n");
        prompt.append("请只返回意图代码，多个意图用逗号分隔，例如：CUSTOMER_INFO,SHOP_INFO\n");
        prompt.append("如果都不匹配，返回：GENERAL_HELP");
        
        return prompt.toString();
    }

    /**
     * 解析AI返回的意图响应
     */
    private List<String> parseIntentResponse(String aiResponse) {
        List<String> detectedIntents = new ArrayList<>();
        
        if (aiResponse != null && !aiResponse.trim().isEmpty()) {
            String[] parts = aiResponse.trim().split(",");
            for (String part : parts) {
                String intent = part.trim().toUpperCase();
                if (INTENT_OPTIONS.contains(intent)) {
                    detectedIntents.add(intent);
                }
            }
        }
        
        // 如果没有检测到有效意图，返回默认帮助
        if (detectedIntents.isEmpty()) {
            detectedIntents.add("GENERAL_HELP");
        }
        
        return detectedIntents;
    }

    /**
     * 根据意图查询相关数据
     */
    private Map<String, Object> queryDataByIntents(Long customerId, List<String> intentTypes, String userMessage) {
        Map<String, Object> queryResults = new HashMap<>();
        
        for (String intentType : intentTypes) {
            switch (intentType) {
                case "CUSTOMER_INFO":
                    Customer customer = getCustomerInfo(customerId);
                    queryResults.put("customerInfo", customer);
                    break;
                    
                case "SHOP_INFO":
                    List<Shop> shops = getCustomerShops(customerId);
                    queryResults.put("shopInfo", shops);
                    break;
                    
                case "SHOP_STATISTICS":
                    Map<String, Object> statistics = getCustomerStatistics(customerId);
                    queryResults.put("shopStatistics", statistics);
                    break;
                    
                case "LISTING_STATISTICS":
                    // 从用户消息中提取时间区间，如果没有则使用默认值
                    String[] dateRange = extractDateRangeFromMessage(userMessage);
                    String startDate = dateRange[0];
                    String endDate = dateRange[1];
                    Map<String, Object> listingStatistics = getCustomerListingStatistics(customerId, startDate, endDate);
                    queryResults.put("listingStatistics", listingStatistics);
                    break;
                    
                case "GENERAL_HELP":
                    queryResults.put("generalHelp", buildHelpInfo());
                    break;
                    
                default:
                    // 其他意图暂时返回提示信息
                    queryResults.put(intentType.toLowerCase() + "Info", intentType + "查询功能待实现");
                    break;
            }
        }
        
        return queryResults;
    }

    /**
     * 构建帮助信息
     */
    private String buildHelpInfo() {
        return "我可以帮您查询以下信息：\n" +
               "1. 客户基本信息（姓名、联系方式、地址等）\n" +
               "2. 店铺信息（店铺名称、品牌、公司、法人等）\n" +
               "3. 店铺统计数据（店铺数量、活跃状态等）\n" +
               "4. 刊登统计数据（各店铺的刊登数量，支持时间区间查询）\n" +
               "5. 订单信息\n" +
               "6. 产品信息\n" +
               "7. 账户信息\n\n" +
               "刊登统计查询示例：\n" +
               "- \"查询刊登数量\"（默认最近30天）\n" +
               "- \"查询昨天的刊登数量\"\n" +
               "- \"查询上周的刊登情况\"\n" +
               "- \"查询2024年1月的刊登统计\"\n" +
               "- \"查询从1月1号到1月31号的刊登数量\"\n" +
               "- \"查询本季度各店铺的刊登数据\"\n\n" +
               "请直接告诉我您想查询什么信息。";
    }

    /**
     * 构建AI提示词（新版本）
     */
    private String buildAIPrompt(Long customerId, String userMessage, List<String> intentTypes, Map<String, Object> queryResults) {
        StringBuilder prompt = new StringBuilder();
        
        // 系统角色设定
        prompt.append("你是一个专业的客户服务AI助手，专门为零亿出海公司的客户提供信息查询和咨询服务。");
        prompt.append("请根据客户的问题和提供的数据，提供准确、友好的回答。");
        prompt.append("回答要简洁明了，如果信息不足，请说明需要补充的信息。\n\n");
        
        // 用户问题
        prompt.append("客户问题：").append(userMessage).append("\n\n");
        
        // 查询到的数据
        prompt.append("相关数据：\n");
        for (Map.Entry<String, Object> entry : queryResults.entrySet()) {
            prompt.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        prompt.append("\n");
        
        // 意图类型
        prompt.append("检测到的意图：").append(String.join(", ", intentTypes)).append("\n\n");
        
        prompt.append("请根据以上信息回答客户问题，用中文回答，语气要友好专业。");
        
        return prompt.toString();
    }
    
    /**
     * 从用户消息中提取时间区间
     * 
     * @param message 用户消息
     * @return [开始日期, 结束日期]
     */
    public String[] extractDateRangeFromMessage(String message) {
        String[] dateRange = new String[2];
        
        // 默认使用最近30天
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        String endDate = sdf.format(calendar.getTime());
        
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        String startDate = sdf.format(calendar.getTime());
        
        // 如果消息为空或没有时间相关词汇，使用默认值
        if (message == null || message.isEmpty() || !containsTimeRelatedWords(message)) {
            dateRange[0] = startDate;
            dateRange[1] = endDate;
            return dateRange;
        }
        
        // 使用AI分析时间区间
        try {
            String timeAnalysisPrompt = buildTimeAnalysisPrompt(message);
            String aiResponse = DSV3ModelUtil.chat(timeAnalysisPrompt);
            String[] extractedDates = parseTimeAnalysisResponse(aiResponse);
            
            if (extractedDates != null && extractedDates.length == 2) {
                dateRange[0] = extractedDates[0];
                dateRange[1] = extractedDates[1];
            } else {
                // AI解析失败，使用默认值
                dateRange[0] = startDate;
                dateRange[1] = endDate;
            }
        } catch (Exception e) {
            // 异常情况下使用默认值
            dateRange[0] = startDate;
            dateRange[1] = endDate;
        }
        
        return dateRange;
    }
    
    /**
     * 检查消息是否包含时间相关词汇
     */
    private boolean containsTimeRelatedWords(String message) {
        String[] timeKeywords = {
            "今天", "今日", "昨天", "昨日", "明天", "明日",
            "本周", "这周", "上周", "下周", "本月", "这个月", "上月", "下月",
            "年", "月", "日", "号", "天", "周", "星期",
            "到", "至", "从", "开始", "结束", "期间", "区间"
        };
        
        for (String keyword : timeKeywords) {
            if (message.contains(keyword)) {
                return true;
            }
        }
        
        // 检查是否包含日期格式
        Pattern datePattern = Pattern.compile("\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}");
        return datePattern.matcher(message).find();
    }
    
    /**
     * 构建时间分析提示词
     */
    private String buildTimeAnalysisPrompt(String message) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个时间分析专家。请分析以下用户消息中的时间信息，并返回标准格式的日期区间。\n\n");
        prompt.append("规则：\n");
        prompt.append("1. 如果用户提到具体日期，请使用该日期\n");
        prompt.append("2. 如果用户提到相对时间（如今天、昨天、本周等），请根据当前日期计算具体日期\n");
        prompt.append("3. 如果用户只提到一个日期，请根据上下文判断是开始日期还是结束日期\n");
        prompt.append("4. 如果用户没有明确提到时间，请返回最近30天的日期区间\n");
        prompt.append("5. 日期格式必须是 yyyy-MM-dd\n");
        prompt.append("6. 只返回两个日期，用逗号分隔，例如：2024-01-01,2024-01-31\n\n");
        prompt.append("当前日期：").append(new SimpleDateFormat("yyyy-MM-dd").format(new Date())).append("\n\n");
        prompt.append("用户消息：").append(message).append("\n\n");
        prompt.append("请分析并返回日期区间（格式：开始日期,结束日期）：");
        
        return prompt.toString();
    }
    
    /**
     * 解析AI返回的时间分析结果
     */
    private String[] parseTimeAnalysisResponse(String aiResponse) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return null;
        }
        
        // 清理响应文本，提取日期部分
        String cleanedResponse = aiResponse.trim();
        
        // 匹配日期格式：yyyy-MM-dd,yyyy-MM-dd
        Pattern datePattern = Pattern.compile("(\\d{4}-\\d{1,2}-\\d{1,2}),(\\d{4}-\\d{1,2}-\\d{1,2})");
        Matcher matcher = datePattern.matcher(cleanedResponse);
        
        if (matcher.find()) {
            String startDate = matcher.group(1);
            String endDate = matcher.group(2);
            return new String[]{startDate, endDate};
        }
        
        // 如果没有找到标准格式，尝试其他可能的格式
        String[] parts = cleanedResponse.split(",");
        if (parts.length >= 2) {
            String startDate = parts[0].trim();
            String endDate = parts[1].trim();
            
            // 验证日期格式
            if (isValidDateFormat(startDate) && isValidDateFormat(endDate)) {
                return new String[]{startDate, endDate};
            }
        }
        
        return null;
    }
    
    /**
     * 验证日期格式是否正确
     */
    private boolean isValidDateFormat(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return false;
        }
        
        Pattern datePattern = Pattern.compile("\\d{4}-\\d{1,2}-\\d{1,2}");
        return datePattern.matcher(dateStr.trim()).matches();
    }
} 