<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.yimai.mapper.AiModelsMapper">

    <resultMap type="AiModels" id="AiModelsResult">
        <result property="id"    column="id"    />
        <result property="modelName"    column="model_name"    />
        <result property="apiKey"    column="api_key"    />
        <result property="apiSecret"    column="api_secret"    />
    </resultMap>

    <sql id="selectAiModelsVo">
        select id, model_name, api_key, api_secret from models
    </sql>

    <select id="selectAiModelsList" parameterType="AiModels" resultMap="AiModelsResult">
        <include refid="selectAiModelsVo"/>
        <where>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="apiKey != null  and apiKey != ''"> and api_key = #{apiKey}</if>
            <if test="apiSecret != null  and apiSecret != ''"> and api_secret = #{apiSecret}</if>
        </where>
    </select>

    <select id="selectAiModelsById" parameterType="Long" resultMap="AiModelsResult">
        <include refid="selectAiModelsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAiModels" parameterType="AiModels">
        insert into models
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="modelName != null">model_name,</if>
            <if test="apiKey != null">api_key,</if>
            <if test="apiSecret != null">api_secret,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="apiKey != null">#{apiKey},</if>
            <if test="apiSecret != null">#{apiSecret},</if>
        </trim>
    </insert>

    <update id="updateAiModels" parameterType="AiModels">
        update models
        <trim prefix="SET" suffixOverrides=",">
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="apiKey != null">api_key = #{apiKey},</if>
            <if test="apiSecret != null">api_secret = #{apiSecret},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAiModelsById" parameterType="Long">
        delete from models where id = #{id}
    </delete>

    <delete id="deleteAiModelsByIds" parameterType="String">
        delete from models where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>