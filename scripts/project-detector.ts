/**
 * Java Project Type Detector
 * 
 * Comprehensive project detection system for Java projects
 * Supports Maven, Gradle, Spring Boot, and plain Java projects
 * Includes caching mechanism for improved performance
 */

import * as fs from 'fs';
import * as path from 'path';
import * as xml2js from 'xml2js';

// Project type definitions
export enum ProjectType {
    MAVEN_SPRING_BOOT = 'Maven Spring Boot',
    MAVEN_STANDARD = 'Maven Standard',
    GRADLE_SPRING_BOOT = 'Gradle Spring Boot',
    GRADLE_STANDARD = 'Gradle Standard',
    PLAIN_JAVA = 'Plain Java',
    UNKNOWN = 'Unknown'
}

export enum BuildTool {
    MAVEN = 'Maven',
    GRADLE = 'Gradle',
    NONE = 'None'
}

// Data models
export interface MainClassInfo {
    className: string;
    filePath: string;
    packageName: string;
    isSpringBootApp: boolean;
    hasMainMethod: boolean;
}

export interface DependencyInfo {
    groupId: string;
    artifactId: string;
    version?: string;
    scope?: string;
}

export interface ProjectInfo {
    rootPath: string;
    projectType: ProjectType;
    buildTool: BuildTool;
    mainClasses: MainClassInfo[];
    dependencies: DependencyInfo[];
    javaVersion?: string;
    sourceDirectories: string[];
    resourceDirectories: string[];
    isSpringBoot: boolean;
    hasTests: boolean;
    lastDetected: Date;
}

// Cache interface
interface ProjectCache {
    [projectPath: string]: {
        info: ProjectInfo;
        lastModified: Date;
        fileHashes: { [file: string]: string };
    };
}

export class ProjectDetector {
    private cache: ProjectCache = {};
    private cacheFile: string;

    constructor(cacheDir: string = '.kiro/cache') {
        this.cacheFile = path.join(cacheDir, 'project-detection-cache.json');
        this.loadCache();
    }

    /**
     * Main detection method - detects project type and structure
     */
    public async detectProject(projectPath: string = '.'): Promise<ProjectInfo> {
        const absolutePath = path.resolve(projectPath);

        // Check cache first
        const cachedInfo = await this.getCachedInfo(absolutePath);
        if (cachedInfo) {
            console.log('[CACHE] Using cached project information');
            return cachedInfo;
        }

        console.log('[DETECT] Starting Java project detection...');

        const projectInfo: ProjectInfo = {
            rootPath: absolutePath,
            projectType: ProjectType.UNKNOWN,
            buildTool: BuildTool.NONE,
            mainClasses: [],
            dependencies: [],
            sourceDirectories: [],
            resourceDirectories: [],
            isSpringBoot: false,
            hasTests: false,
            lastDetected: new Date()
        };

        // Detect build tool and project type
        await this.detectBuildTool(projectInfo);

        // Find main classes
        await this.findMainClasses(projectInfo);

        // Detect source and resource directories
        await this.detectDirectories(projectInfo);

        // Check for tests
        await this.detectTests(projectInfo);

        // Determine final project type
        this.determineProjectType(projectInfo);

        // Cache the results
        await this.cacheProjectInfo(projectInfo);

        console.log(`[SUCCESS] Detected ${projectInfo.projectType} project`);
        return projectInfo;
    }

    /**
     * Detect build tool (Maven or Gradle)
     */
    private async detectBuildTool(projectInfo: ProjectInfo): Promise<void> {
        const pomPath = path.join(projectInfo.rootPath, 'pom.xml');
        const gradlePath = path.join(projectInfo.rootPath, 'build.gradle');
        const gradleKtsPath = path.join(projectInfo.rootPath, 'build.gradle.kts');

        if (fs.existsSync(pomPath)) {
            console.log('[FOUND] Maven project (pom.xml detected)');
            projectInfo.buildTool = BuildTool.MAVEN;
            await this.parseMavenProject(projectInfo, pomPath);
        } else if (fs.existsSync(gradlePath) || fs.existsSync(gradleKtsPath)) {
            console.log('[FOUND] Gradle project (build.gradle detected)');
            projectInfo.buildTool = BuildTool.GRADLE;
            const buildFile = fs.existsSync(gradlePath) ? gradlePath : gradleKtsPath;
            await this.parseGradleProject(projectInfo, buildFile);
        }
    }

    /**
     * Parse Maven pom.xml file
     */
    private async parseMavenProject(projectInfo: ProjectInfo, pomPath: string): Promise<void> {
        try {
            const pomContent = fs.readFileSync(pomPath, 'utf-8');
            const parser = new xml2js.Parser();
            const result = await parser.parseStringPromise(pomContent);

            const project = result.project;

            // Extract basic project info
            if (project.properties && project.properties[0]['java.version']) {
                projectInfo.javaVersion = project.properties[0]['java.version'][0];
            }

            // Extract dependencies
            if (project.dependencies && project.dependencies[0].dependency) {
                for (const dep of project.dependencies[0].dependency) {
                    const dependency: DependencyInfo = {
                        groupId: dep.groupId[0],
                        artifactId: dep.artifactId[0],
                        version: dep.version ? dep.version[0] : undefined,
                        scope: dep.scope ? dep.scope[0] : undefined
                    };

                    projectInfo.dependencies.push(dependency);

                    // Check for Spring Boot
                    if (dependency.artifactId.includes('spring-boot')) {
                        projectInfo.isSpringBoot = true;
                        console.log('[SPRING BOOT] Spring Boot dependency found');
                    }
                }
            }

            // Check parent for Spring Boot
            if (project.parent && project.parent[0].artifactId &&
                project.parent[0].artifactId[0].includes('spring-boot')) {
                projectInfo.isSpringBoot = true;
                console.log('[SPRING BOOT] Spring Boot parent found');
            }

        } catch (error) {
            console.warn(`[WARNING] Could not parse pom.xml: ${error}`);
        }
    }

    /**
     * Parse Gradle build file
     */
    private async parseGradleProject(projectInfo: ProjectInfo, buildPath: string): Promise<void> {
        try {
            const buildContent = fs.readFileSync(buildPath, 'utf-8');

            // Check for Spring Boot plugin
            if (buildContent.includes('spring-boot') ||
                buildContent.includes('org.springframework.boot')) {
                projectInfo.isSpringBoot = true;
                console.log('[SPRING BOOT] Spring Boot plugin found');
            }

            // Extract Java version
            const javaVersionMatch = buildContent.match(/sourceCompatibility\s*=\s*['"]?(\d+(?:\.\d+)?)/);
            if (javaVersionMatch) {
                projectInfo.javaVersion = javaVersionMatch[1];
            }

        } catch (error) {
            console.warn(`[WARNING] Could not parse build.gradle: ${error}`);
        }
    }

    /**
     * Find all main classes in the project
     */
    private async findMainClasses(projectInfo: ProjectInfo): Promise<void> {
        console.log('[SEARCH] Looking for main classes...');

        const javaFiles = this.findJavaFiles(projectInfo.rootPath);

        for (const filePath of javaFiles) {
            try {
                const content = fs.readFileSync(filePath, 'utf-8');
                const mainClassInfo = this.analyzeJavaFile(filePath, content);

                if (mainClassInfo && mainClassInfo.hasMainMethod) {
                    projectInfo.mainClasses.push(mainClassInfo);

                    if (mainClassInfo.isSpringBootApp) {
                        projectInfo.isSpringBoot = true;
                        console.log(`[MAIN CLASS] ${mainClassInfo.className} (Spring Boot Application)`);
                    } else {
                        console.log(`[MAIN CLASS] ${mainClassInfo.className}`);
                    }
                }
            } catch (error) {
                console.warn(`[WARNING] Could not parse ${filePath}: ${error}`);
            }
        }
    }

    /**
     * Analyze a Java file for main method and annotations
     */
    private analyzeJavaFile(filePath: string, content: string): MainClassInfo | null {
        // Extract package name
        const packageMatch = content.match(/package\s+([\w\.]+);/);
        const packageName = packageMatch ? packageMatch[1] : '';

        // Extract class name
        const fileName = path.basename(filePath, '.java');
        const className = packageName ? `${packageName}.${fileName}` : fileName;

        // Check for main method
        const hasMainMethod = /public\s+static\s+void\s+main\s*\(\s*String\s*\[\s*\]\s*\w+\s*\)/.test(content);

        // Check for Spring Boot application annotation
        const isSpringBootApp = /@SpringBootApplication/.test(content);

        if (hasMainMethod || isSpringBootApp) {
            return {
                className,
                filePath,
                packageName,
                isSpringBootApp,
                hasMainMethod
            };
        }

        return null;
    }

    /**
     * Find all Java files recursively
     */
    private findJavaFiles(rootPath: string): string[] {
        const javaFiles: string[] = [];

        const scanDirectory = (dirPath: string) => {
            try {
                const entries = fs.readdirSync(dirPath, { withFileTypes: true });

                for (const entry of entries) {
                    const fullPath = path.join(dirPath, entry.name);

                    if (entry.isDirectory()) {
                        // Skip common non-source directories
                        if (!['node_modules', '.git', 'target', 'build', '.gradle'].includes(entry.name)) {
                            scanDirectory(fullPath);
                        }
                    } else if (entry.isFile() && entry.name.endsWith('.java')) {
                        javaFiles.push(fullPath);
                    }
                }
            } catch (error) {
                console.warn(`[WARNING] Could not scan directory ${dirPath}: ${error}`);
            }
        };

        scanDirectory(rootPath);
        return javaFiles;
    }

    /**
     * Detect source and resource directories
     */
    private async detectDirectories(projectInfo: ProjectInfo): Promise<void> {
        const commonSourceDirs = [
            'src/main/java',
            'src/test/java',
            'src/java',
            'java',
            'src'
        ];

        const commonResourceDirs = [
            'src/main/resources',
            'src/test/resources',
            'src/resources',
            'resources'
        ];

        for (const dir of commonSourceDirs) {
            const fullPath = path.join(projectInfo.rootPath, dir);
            if (fs.existsSync(fullPath)) {
                projectInfo.sourceDirectories.push(dir);
            }
        }

        for (const dir of commonResourceDirs) {
            const fullPath = path.join(projectInfo.rootPath, dir);
            if (fs.existsSync(fullPath)) {
                projectInfo.resourceDirectories.push(dir);
            }
        }
    }

    /**
     * Detect test files
     */
    private async detectTests(projectInfo: ProjectInfo): Promise<void> {
        const testDirs = ['src/test', 'test', 'tests'];

        for (const testDir of testDirs) {
            const fullPath = path.join(projectInfo.rootPath, testDir);
            if (fs.existsSync(fullPath)) {
                const testFiles = this.findJavaFiles(fullPath);
                if (testFiles.length > 0) {
                    projectInfo.hasTests = true;
                    console.log(`[FOUND] Test files detected (${testFiles.length} files)`);
                    break;
                }
            }
        }
    }

    /**
     * Determine final project type based on collected information
     */
    private determineProjectType(projectInfo: ProjectInfo): void {
        if (projectInfo.buildTool === BuildTool.MAVEN) {
            projectInfo.projectType = projectInfo.isSpringBoot ?
                ProjectType.MAVEN_SPRING_BOOT : ProjectType.MAVEN_STANDARD;
        } else if (projectInfo.buildTool === BuildTool.GRADLE) {
            projectInfo.projectType = projectInfo.isSpringBoot ?
                ProjectType.GRADLE_SPRING_BOOT : ProjectType.GRADLE_STANDARD;
        } else if (projectInfo.mainClasses.length > 0) {
            projectInfo.projectType = ProjectType.PLAIN_JAVA;
        } else {
            projectInfo.projectType = ProjectType.UNKNOWN;
        }
    }

    /**
     * Cache management methods
     */
    private loadCache(): void {
        try {
            if (fs.existsSync(this.cacheFile)) {
                const cacheData = fs.readFileSync(this.cacheFile, 'utf-8');
                this.cache = JSON.parse(cacheData);
            }
        } catch (error) {
            console.warn(`[WARNING] Could not load cache: ${error}`);
            this.cache = {};
        }
    }

    private async saveCache(): Promise<void> {
        try {
            const cacheDir = path.dirname(this.cacheFile);
            if (!fs.existsSync(cacheDir)) {
                fs.mkdirSync(cacheDir, { recursive: true });
            }

            fs.writeFileSync(this.cacheFile, JSON.stringify(this.cache, null, 2));
        } catch (error) {
            console.warn(`[WARNING] Could not save cache: ${error}`);
        }
    }

    private async getCachedInfo(projectPath: string): Promise<ProjectInfo | null> {
        const cached = this.cache[projectPath];
        if (!cached) return null;

        // Check if cache is still valid (check file modification times)
        const criticalFiles = [
            path.join(projectPath, 'pom.xml'),
            path.join(projectPath, 'build.gradle'),
            path.join(projectPath, 'build.gradle.kts')
        ];

        for (const file of criticalFiles) {
            if (fs.existsSync(file)) {
                const stats = fs.statSync(file);
                const cachedHash = cached.fileHashes[file];
                const currentHash = this.getFileHash(file);

                if (cachedHash !== currentHash) {
                    console.log(`[CACHE] Cache invalidated - ${file} has changed`);
                    return null;
                }
            }
        }

        return cached.info;
    }

    private async cacheProjectInfo(projectInfo: ProjectInfo): Promise<void> {
        const fileHashes: { [file: string]: string } = {};

        // Hash critical files
        const criticalFiles = [
            path.join(projectInfo.rootPath, 'pom.xml'),
            path.join(projectInfo.rootPath, 'build.gradle'),
            path.join(projectInfo.rootPath, 'build.gradle.kts')
        ];

        for (const file of criticalFiles) {
            if (fs.existsSync(file)) {
                fileHashes[file] = this.getFileHash(file);
            }
        }

        this.cache[projectInfo.rootPath] = {
            info: projectInfo,
            lastModified: new Date(),
            fileHashes
        };

        await this.saveCache();
    }

    private getFileHash(filePath: string): string {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            return require('crypto').createHash('md5').update(content).digest('hex');
        } catch {
            return '';
        }
    }

    /**
     * Clear cache for a specific project or all projects
     */
    public async clearCache(projectPath?: string): Promise<void> {
        if (projectPath) {
            delete this.cache[path.resolve(projectPath)];
        } else {
            this.cache = {};
        }
        await this.saveCache();
    }

    /**
     * Get project structure summary
     */
    public getProjectSummary(projectInfo: ProjectInfo): string {
        const lines = [
            '='.repeat(50),
            'Java Project Detection Summary',
            '='.repeat(50),
            `Project Type: ${projectInfo.projectType}`,
            `Build Tool: ${projectInfo.buildTool}`,
            `Spring Boot: ${projectInfo.isSpringBoot ? 'Yes' : 'No'}`,
            `Java Version: ${projectInfo.javaVersion || 'Unknown'}`,
            `Main Classes: ${projectInfo.mainClasses.length}`,
            `Dependencies: ${projectInfo.dependencies.length}`,
            `Has Tests: ${projectInfo.hasTests ? 'Yes' : 'No'}`,
            `Source Directories: ${projectInfo.sourceDirectories.join(', ') || 'None'}`,
            `Resource Directories: ${projectInfo.resourceDirectories.join(', ') || 'None'}`,
            ''
        ];

        if (projectInfo.mainClasses.length > 0) {
            lines.push('Main Classes:');
            for (const mainClass of projectInfo.mainClasses) {
                lines.push(`  - ${mainClass.className}`);
                lines.push(`    File: ${mainClass.filePath}`);
                lines.push(`    Spring Boot: ${mainClass.isSpringBootApp ? 'Yes' : 'No'}`);
            }
        }

        return lines.join('\n');
    }
}

// Export for use in other modules
export default ProjectDetector;